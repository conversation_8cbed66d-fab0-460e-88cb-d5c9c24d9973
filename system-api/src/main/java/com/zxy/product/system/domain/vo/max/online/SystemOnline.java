package com.zxy.product.system.domain.vo.max.online;

import java.io.Serializable;

/**
 * 系统最大在线回显VO
 * <AUTHOR>
 * @date 2025年03月16日 3:59
 */
public class SystemOnline implements Serializable {
    private static final long serialVersionUID = -5420803656656232282L;

    /**系统最大在线人数*/
    private Integer maxOnlines;

    /**App 系统最大在线人数*/
    private Integer maxOnlinesApp;

    /**PC 系统最大在线人数*/
    private Integer maxOnlinesPc;

    public Integer getMaxOnlines() { return maxOnlines; }

    public void setMaxOnlines(Integer maxOnlines) { this.maxOnlines = maxOnlines; }

    public Integer getMaxOnlinesApp() { return maxOnlinesApp; }

    public void setMaxOnlinesApp(Integer maxOnlinesApp) { this.maxOnlinesApp = maxOnlinesApp; }

    public Integer getMaxOnlinesPc() { return maxOnlinesPc; }

    public void setMaxOnlinesPc(Integer maxOnlinesPc) { this.maxOnlinesPc = maxOnlinesPc; }

    @Override
    public String toString() {
        return "SystemOnline{" +
                "maxOnlines=" + maxOnlines +
                ", maxOnlinesApp=" + maxOnlinesApp +
                ", maxOnlinesPc=" + maxOnlinesPc +
                '}';
    }
}
