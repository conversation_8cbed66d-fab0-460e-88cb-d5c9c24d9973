package com.zxy.product.system.domain.vo;

import java.io.Serializable;

/**
 * 首页转化回显：讲师榜VO
 * <AUTHOR>
 * @date 2024年11月12日 16:40
 */
public class ConvertLectureVO extends HomeParentVO implements Serializable {
    private static final long serialVersionUID = -6537675189095454327L;

    /**讲师Id*/
    private String lecturerId;
    /**讲师VO*/
    private LecturerVO lecturer;

    public String getLecturerId() { return lecturerId; }
    public void setLecturerId(String lecturerId) { this.lecturerId = lecturerId; }
    public LecturerVO getLecturer() { return lecturer; }
    public void setLecturer(LecturerVO lecturer) { this.lecturer = lecturer; }

    @Override
    public String toString() {
        return "HomeLecturerVO{" +
                "lecturerId='" + lecturerId + '\'' +
                ", lecturer=" + lecturer +
                '}';
    }

    /**首页讲师回显VO*/
    public static class LecturerVO implements Serializable{
        private static final long serialVersionUID = 7832895788472847102L;

        /**讲师属性*/
        private String attributeId;
        /**讲师头像URL*/
        private String coverPath;
        /**机构 f_cooperation_type＝1有效*/
        private String institutions;
        /**职务|职称*/
        private String jobName;
        /**讲师级别名称*/
        private String levelName;
        /**讲师名称  f_type=1时有效*/
        private String name;
        /**类型 0 内部讲师  1 外部讲师*/
        private Integer type;
        /**讲师单位*/
        private String unit;

        public String getAttributeId() { return attributeId; }
        public void setAttributeId(String attributeId) { this.attributeId = attributeId; }
        public String getCoverPath() { return coverPath; }
        public void setCoverPath(String coverPath) { this.coverPath = coverPath; }
        public String getInstitutions() { return institutions; }
        public void setInstitutions(String institutions) { this.institutions = institutions; }
        public String getJobName() { return jobName; }
        public void setJobName(String jobName) { this.jobName = jobName; }
        public String getLevelName() { return levelName; }
        public void setLevelName(String levelName) { this.levelName = levelName; }
        public String getName() { return name; }
        public void setName(String name) { this.name = name; }
        public Integer getType() { return type; }
        public void setType(Integer type) { this.type = type; }
        public String getUnit() { return unit; }
        public void setUnit(String unit) { this.unit = unit; }

        @Override
        public String toString() {
            return "LecturerVO{" +
                    "attributeId='" + attributeId + '\'' +
                    ", coverPath='" + coverPath + '\'' +
                    ", institutions='" + institutions + '\'' +
                    ", jobName='" + jobName + '\'' +
                    ", levelName='" + levelName + '\'' +
                    ", name='" + name + '\'' +
                    ", type=" + type +
                    ", unit='" + unit + '\'' +
                    '}';
        }
    }

}
