package com.zxy.product.system.domain.vo;

import com.alibaba.fastjson.annotation.JSONField;
import com.zxy.product.system.util.HomeCfgSerializer;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * 首页配置VO
 * <AUTHOR>
 * @date 2024年08月02日 9:22
 */
public  class HomeCfgVO implements Serializable {
    private static final long serialVersionUID = 7795473594227406043L;

    /**首页配置名称*/
    private String name;

    /**首页配置数据集合*/
    private HomeCfgModelVO value;

    /**首页配置排序字段*/
    private Integer sort;

    public String getName() { return name; }

    public void setName(String name) { this.name = name; }

    public HomeCfgModelVO  getValue() { return value; }

    public void setValue(HomeCfgModelVO value) { this.value = value; }

    public Integer getSort() { return sort; }

    public void setSort(Integer sort) { this.sort = sort; }

    @Override
    public String toString() {
        return "HomeCfgVO{" +
                "name='" + name + '\'' +
                ", value=" + value +
                ", sort=" + sort +
                '}';
    }

    /**首页配置模块VO*/
    public static class HomeCfgModelVO implements Serializable {
        private static final long serialVersionUID = 9194509260195823022L;

        /**首页配置编码*/
        private String configCode;

        /**首页配置Id*/
        private String configId;

        /**首页配置名称*/
        private String name;

        /**首页配置样式*/
        private String style;

        /**首页跳转状态*/
        private Integer linkState;

        /**首页跳转地址*/
        private String linkAddress;

        /**首页配置数据集合*/
        @JSONField(deserializeUsing = HomeCfgSerializer.class)
        private List<? extends HomeParentVO> list;

        /**首页配置数据集合*/
        private Map<String, Object> data;

        public String getConfigCode() { return configCode; }

        public void setConfigCode(String configCode) { this.configCode = configCode; }

        public String getConfigId() { return configId; }

        public void setConfigId(String configId) { this.configId = configId; }

        public String getName() { return name; }

        public void setName(String name) { this.name = name; }

        public String getStyle() { return style; }

        public void setStyle(String style) { this.style = style; }

        public List<? extends HomeParentVO> getList() { return list; }

        public void setList(List<? extends HomeParentVO> list) { this.list = list; }

        public Map<String, Object> getData() { return data; }

        public void setData(Map<String, Object> data) { this.data = data; }

        public Integer getLinkState() { return linkState; }

        public void setLinkState(Integer linkState) { this.linkState = linkState; }

        public String getLinkAddress() { return linkAddress; }

        public void setLinkAddress(String linkAddress) { this.linkAddress = linkAddress; }

        @Override
        public String toString() {
            return "HomeCfgModelVO{" +
                    "configCode='" + configCode + '\'' +
                    ", configId='" + configId + '\'' +
                    ", name='" + name + '\'' +
                    ", style='" + style + '\'' +
                    ", linkState=" + linkState +
                    ", linkAddress='" + linkAddress + '\'' +
                    ", list=" + list +
                    ", data=" + data +
                    '}';
        }
    }
}
