package com.zxy.product.system.domain.vo;

import com.zxy.product.system.entity.HomeNav;

import java.io.Serializable;
import java.util.List;

/**
 * 首页CDN文件格式VO
 * <AUTHOR>
 * @date 2024年11月07日 16:26
 */
public class HomeParentCfgVO implements Serializable {
    private static final long serialVersionUID = -7218565013043401999L;

    /**首页配置项数据集合*/
    private List<HomeCfgVO> cfgCollect;

    /**首页导航数据集合*/
    private List<HomeNav> navCollect;

    public List<HomeCfgVO> getCfgCollect() { return cfgCollect; }

    public void setCfgCollect(List<HomeCfgVO> cfgCollect) { this.cfgCollect = cfgCollect; }

    public List<HomeNav> getNavCollect() { return navCollect; }

    public void setNavCollect(List<HomeNav> navCollect) { this.navCollect = navCollect; }

    @Override
    public String toString() {
        return "HomeParentCfgVO{" +
                "cfgCollect=" + cfgCollect +
                ", navCollect=" + navCollect +
                '}';
    }
}
