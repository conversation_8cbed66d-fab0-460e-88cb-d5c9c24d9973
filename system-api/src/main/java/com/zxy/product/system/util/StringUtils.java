package com.zxy.product.system.util;


import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.Calendar;
import java.util.Date;
import java.util.Optional;

/**
 * Created by keeley on 16/10/17.
 */
public interface StringUtils {
    public static final String YYYY_MM_DD = "yyyy-MM-dd";
    public static final String YYYY_MM_DD_HH_MM_SS = "yyyy-MM-dd HH:mm:ss";
    public static final String YYYY_MM_DD_HH_MM = "yyyy-MM-dd HH:mm";
    public static final String MM_DD_HH_MM = "MM-dd HH:mm";
    public static final String HH_MM = "HH:mm";
    DateTimeFormatter DEFAULTFORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
    static long dateString2OptionalLong(String t) {
        return Date.from(LocalDate.parse(t).atStartOfDay().atZone(ZoneId.systemDefault()).toInstant()).getTime();
    }
    static Optional<Long> dateString2OptionalLong(Optional<String> value) {
        return value.map(StringUtils::dateString2OptionalLong);
    }
    static Optional<Long> dateString2OptionalLongMore(Optional<String> value) {
        return value.map( x -> StringUtils.dateString2Long(x) + 24*60*60*1000);
    }
    static long dateString2Long(String t) {
        return Date.from(LocalDate.parse(t).atStartOfDay().atZone(ZoneId.systemDefault()).toInstant()).getTime();
    }
    static long dateTimeString2OptionalLong(String t) {
        return Date.from(LocalDateTime.parse(t,DEFAULTFORMATTER).atZone(ZoneId.systemDefault()).toInstant()).getTime();
    }
    static Optional<Long> dateTimeString2OptionalLong(Optional<String> value) {
        return value.map(StringUtils::dateTimeString2OptionalLong);
    }

    static long getCurrentTime(){
        return java.util.Date.from(LocalDateTime.now().atZone(ZoneId.systemDefault()).toInstant()).getTime();
    }

    /**
     * Long类型日期转String类型
     * @param dateLong
     * @param format
     * @return
     */
    public static String dateLongToString(Long dateLong,String format){
        SimpleDateFormat sdf = new SimpleDateFormat(format);
        Date date = new Date(dateLong);
        return sdf.format(date);
    }


    /**
     * 讨论区时间转换
     * @param createTime
     * @return
     */
    public static String discussTime(Long createTime){
        Calendar cCreate = Calendar.getInstance();
        cCreate.setTimeInMillis(createTime);
        Calendar cNow = Calendar.getInstance();
        cNow.setTime(new Date());
        if(cCreate.get(Calendar.YEAR) < cNow.get(Calendar.YEAR)){
            return dateLongToString(createTime,YYYY_MM_DD_HH_MM_SS);
        }
        if(cCreate.get(Calendar.MONTH) < cNow.get(Calendar.MONTH)){
            return dateLongToString(createTime,MM_DD_HH_MM);
        }
        if(cNow.get(Calendar.DAY_OF_MONTH) - cCreate.get(Calendar.DAY_OF_MONTH) >= 2 ){
            return dateLongToString(createTime,MM_DD_HH_MM);
        }
        if(cCreate.get(Calendar.DAY_OF_MONTH) < cNow.get(Calendar.DAY_OF_MONTH)){
            return "昨日 " + dateLongToString(createTime,HH_MM);
        }
        return "今日 " + dateLongToString(createTime,HH_MM);
    }
}