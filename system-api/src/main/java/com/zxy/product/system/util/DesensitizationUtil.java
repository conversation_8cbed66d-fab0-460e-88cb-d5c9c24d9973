package com.zxy.product.system.util;

import org.springframework.util.StringUtils;

import java.util.Optional;

/**
 * 脱敏
 * <AUTHOR>
 */
public class DesensitizationUtil {

    private final static String MOBILE_RULE="(\\d{3})\\d*(\\d{4})";
    private final static String MOBILE_REPLACE_SYMBOL="$1****$2";
    private final static String EMAIL_RULE="(?<=.{2})[^@]+(?=.{2}@)";
    private final static String SIMPLE_EMAIL_RULE="(?<=.{1})[^@]+(?=@)";

    /**
     * 员工编号脱敏
     * @param employeeId 员工编号
     */
    public static String desensitizeEmployeeId(String employeeId) {
        if (StringUtils.isEmpty(employeeId)) {
            return employeeId;
        }
        if (employeeId.length() == 18) {
            return desensitize18DigitId(employeeId);
        } else if (employeeId.length() == 11 && isNumeric(employeeId)) {
            return desensitize11DigitId(employeeId);
        } else {
            return employeeId; // 其他情况不进行脱敏
        }
    }

    /**
     * 邮箱脱敏
     * 邮箱@前不足4位，保留第1位
     * 邮箱@前>4位，只保留前两位和后两位，中间脱敏为*
     * @param email
     * @return
     */
    public static String desensitizeEmail(Optional<String> email){
        return email.map(t-> {
            if (t.split("@").length > 1) {
                int length = t.split("@")[0].length();
                if (length <= 4 && length > 1) {
                    return t.replaceAll(SIMPLE_EMAIL_RULE, getStars(length-1));
                }
                if (length > 4) {
                    return t.replaceAll(EMAIL_RULE, getStars(length-4));
                }
            }
            return t;
        }).orElse(null);
    }

    /**
     * 手机号加密
     * @param mobile
     * @return
     */
    public static String desensitizeMobile(Optional<String> mobile){
        return mobile.map(t->t.replaceAll(MOBILE_RULE,MOBILE_REPLACE_SYMBOL)).orElse(null);
    }

    private static String desensitize18DigitId(String employeeId) {
        return employeeId.substring(0, 6) + "********" + employeeId.substring(14);
    }

    private static String desensitize11DigitId(String employeeId) {
        return employeeId.substring(0, 3) + "****" + employeeId.substring(7);
    }

    private static boolean isNumeric(String str) {
        for (char c : str.toCharArray()) {
            if (!Character.isDigit(c)) {
                return false;
            }
        }
        return true;
    }

    private static String getStars(int length) {
        StringBuffer sb=new StringBuffer();
        for (int i=0; i<length; i++) {
            sb.append("*");
        }
        return sb.toString();
    }
}
