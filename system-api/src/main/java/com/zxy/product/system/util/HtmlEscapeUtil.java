package com.zxy.product.system.util;

public class HtmlEscapeUtil {

    /**
     * 对输入的字符串进行HTML转义，防止XSS攻击
     *
     * @param input 要转义的字符串
     * @return 转义后的字符串
     */
    public static String escapeHtml(String input) {
        if (input == null || input.isEmpty()) {
            return input;
        }
        return input.replace("&", "&amp;")  // 替换 &
                .replace("<", "&lt;")  // 替换 <
                .replace(">", "&gt;")  // 替换 >
                .replace("\"", "&quot;")  // 替换 "
                .replace("'", "&#x27;")  // 替换 '
                .replace("/", "&#x2F;");  // 替换 /
    }
}
