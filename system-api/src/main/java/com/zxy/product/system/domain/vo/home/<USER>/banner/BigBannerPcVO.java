package com.zxy.product.system.domain.vo.home.big.banner;

import java.io.Serializable;

/**
 * 首页大BANNER：覆写PC回显VO
 * <AUTHOR>
 * @date 2024年11月25日 11:30
 */
public class BigBannerPcVO extends BigBannerParentVO implements Serializable {
    private static final long serialVersionUID = -1715123088561710340L;

    /**大BANNER：配置Id*/
    private String id;

    /**大BANNER：业务Id*/
    private String businessId;

    /**大BANNER：业务类型*/
    private Integer businessType;

    /**大BANNER：URL连接地址*/
    private String linkAddress;

    /**大BANNER：URL类型*/
    private Integer linkType;

    /**大BANNER：PC图片地址*/
    private String pcImagePath;

    /**大BANNER：标题*/
    private String title;

    public String getId() { return id; }

    public void setId(String id) { this.id = id; }

    public String getBusinessId() { return businessId; }

    public void setBusinessId(String businessId) { this.businessId = businessId; }

    public Integer getBusinessType() { return businessType; }

    public void setBusinessType(Integer businessType) { this.businessType = businessType; }

    public String getLinkAddress() { return linkAddress; }

    public void setLinkAddress(String linkAddress) { this.linkAddress = linkAddress; }

    public Integer getLinkType() { return linkType; }

    public void setLinkType(Integer linkType) { this.linkType = linkType; }

    public String getPcImagePath() { return pcImagePath; }

    public void setPcImagePath(String pcImagePath) { this.pcImagePath = pcImagePath; }

    public String getTitle() { return title; }

    public void setTitle(String title) { this.title = title; }

    @Override
    public String toString() {
        return "BigBannerPcVO{" +
                "id='" + id + '\'' +
                ", businessId='" + businessId + '\'' +
                ", businessType=" + businessType +
                ", linkAddress='" + linkAddress + '\'' +
                ", linkType=" + linkType +
                ", pcImagePath='" + pcImagePath + '\'' +
                ", title='" + title + '\'' +
                '}';
    }
}
