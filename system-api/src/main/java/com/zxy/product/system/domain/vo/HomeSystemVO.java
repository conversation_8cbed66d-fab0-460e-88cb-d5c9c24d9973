package com.zxy.product.system.domain.vo;

import com.zxy.product.system.entity.*;

import java.io.Serializable;
import java.util.List;

/**
 * 首页：系统聚合接口VO
 * <AUTHOR>
 * @date 2024年08月01日 16:39
 */
public class HomeSystemVO implements Serializable {
    private static final long serialVersionUID = -8195718679294257627L;

    /** api/v1/system/message-at-me/get-count返回值*/
    private Integer num;

    /** api/v1/system/msg-count返回值*/
    private Integer todoCount;

    /**  api/v1/system/message-notice/getCount返回值*/
    private Integer noticeMsgCount;

    /** api/v1/system/home-config/{id} 返回值*/
    private HomeConfig singleHomeCfg;

    /** api/v1/system/home-config/config返回值*/
    private HomeConfig homeCfg;

    /** api/v1/system/home-nav的返回值*/
    private List<HomeNav> topNavCollect;

    /**  api/v1/system/home-nav/use-clientType的返回值*/
    private List<HomeNav> bottomNavCollect;

    /** api/v1/system/home-footer的返回值*/
    private HomeFooter homeFooter;

    /** /api/v1/system/skin-config/skin的返回值 */
    private SkinConfig skinConfig;

    /** api/v1/system/home-config/organization的返回值 */
    private List<Organization> orgCollect;

    public Integer getNum() { return num; }

    public void setNum(Integer num) { this.num = num; }

    public Integer getTodoCount() { return todoCount; }

    public void setTodoCount(Integer todoCount) { this.todoCount = todoCount; }

    public Integer getNoticeMsgCount() { return noticeMsgCount; }

    public void setNoticeMsgCount(Integer noticeMsgCount) { this.noticeMsgCount = noticeMsgCount; }

    public HomeConfig getSingleHomeCfg() { return singleHomeCfg; }

    public void setSingleHomeCfg(HomeConfig singleHomeCfg) { this.singleHomeCfg = singleHomeCfg; }

    public HomeConfig getHomeCfg() { return homeCfg; }

    public void setHomeCfg(HomeConfig homeCfg) { this.homeCfg = homeCfg; }

    public List<HomeNav> getTopNavCollect() { return topNavCollect; }

    public void setTopNavCollect(List<HomeNav> topNavCollect) { this.topNavCollect = topNavCollect; }

    public List<HomeNav> getBottomNavCollect() { return bottomNavCollect; }

    public void setBottomNavCollect(List<HomeNav> bottomNavCollect) { this.bottomNavCollect = bottomNavCollect; }

    public HomeFooter getHomeFooter() { return homeFooter; }

    public void setHomeFooter(HomeFooter homeFooter) { this.homeFooter = homeFooter; }

    public List<Organization> getOrgCollect() { return orgCollect; }

    public void setOrgCollect(List<Organization> orgCollect) { this.orgCollect = orgCollect; }

    public SkinConfig getSkinConfig() { return skinConfig; }

    public void setSkinConfig(SkinConfig skinConfig) { this.skinConfig = skinConfig; }

    @Override
    public String toString() {
        return "HomeSystemVO{" +
                "num=" + num +
                ", todoCount=" + todoCount +
                ", noticeMsgCount=" + noticeMsgCount +
                ", singleHomeCfg=" + singleHomeCfg +
                ", homeCfg=" + homeCfg +
                ", topNavCollect=" + topNavCollect +
                ", bottomNavCollect=" + bottomNavCollect +
                ", homeFooter=" + homeFooter +
                ", skinConfig=" + skinConfig +
                ", orgCollect=" + orgCollect +
                '}';
    }
}
