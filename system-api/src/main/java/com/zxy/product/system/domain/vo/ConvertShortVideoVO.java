package com.zxy.product.system.domain.vo;

import java.io.Serializable;

/**
 * 首页转化回显：讲师榜VO
 * <AUTHOR>
 * @date 2024年11月14日 21:02
 */
public class ConvertShortVideoVO extends HomeParentVO implements Serializable {
    private static final long serialVersionUID = -3800342533110530012L;

    /**短视频配置VO主键*/
    private String id;

    /**网大上传封面*/
    private String coverPath;

    /**咪咕默认封面路径*/
    private String coverOriginalPath;

    /**短视频标题*/
    private String shortVideoTitle;

    /**短视频Id*/
    private String shortVideoId;

    public String getId() { return id; }

    public void setId(String id) { this.id = id; }

    public String getCoverPath() { return coverPath; }

    public void setCoverPath(String coverPath) { this.coverPath = coverPath; }

    public String getCoverOriginalPath() { return coverOriginalPath; }

    public void setCoverOriginalPath(String coverOriginalPath) { this.coverOriginalPath = coverOriginalPath; }

    public String getShortVideoTitle() { return shortVideoTitle; }

    public void setShortVideoTitle(String shortVideoTitle) { this.shortVideoTitle = shortVideoTitle; }

    public String getShortVideoId() { return shortVideoId; }

    public void setShortVideoId(String shortVideoId) { this.shortVideoId = shortVideoId; }

    @Override
    public String toString() {
        return "ShortVideoCfgVO{" +
                "id='" + id + '\'' +
                ", coverPath='" + coverPath + '\'' +
                ", coverOriginalPath='" + coverOriginalPath + '\'' +
                ", shortVideoTitle='" + shortVideoTitle + '\'' +
                ", shortVideoId='" + shortVideoId + '\'' +
                '}';
    }
}
