package com.zxy.product.system.domain.vo.max.online;


import java.io.Serializable;

/**
 * 限流规则POJO
 * <AUTHOR>
 * @date 2024年12月12日 16:21
 */
public  class FlowRuleConfig implements Serializable {
    private static final long serialVersionUID = -8058405788317144901L;

    /**应用名*/
    private String applicationName;

    /**模块级别开关 0关闭 1开启*/
    private Integer mainSwitch;

    /**限流资源|业务类型*/
    private Integer businessType;

    /**限流规则POJO*/
    private Object flowRule;

    /**是否隐藏 0显示  1隐藏*/
    private Integer isHide;

    public String getApplicationName() { return applicationName; }

    public void setApplicationName(String applicationName) { this.applicationName = applicationName; }

    public Integer getMainSwitch() { return mainSwitch; }

    public void setMainSwitch(Integer mainSwitch) { this.mainSwitch = mainSwitch; }

    public Integer getBusinessType() { return businessType; }

    public void setBusinessType(Integer businessType) { this.businessType = businessType; }

    public Object getFlowRule() { return flowRule; }

    public void setFlowRule(Object flowRule) { this.flowRule = flowRule; }

    public Integer getIsHide() { return isHide; }

    public void setIsHide(Integer isHide) { this.isHide = isHide; }

    @Override
    public String toString() {
        return "FlowRuleConfig{" +
                "applicationName='" + applicationName + '\'' +
                ", mainSwitch=" + mainSwitch +
                ", businessType=" + businessType +
                ", flowRule=" + flowRule +
                ", isHide=" + isHide +
                '}';
    }
}
