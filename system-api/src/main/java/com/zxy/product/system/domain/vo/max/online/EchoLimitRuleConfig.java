package com.zxy.product.system.domain.vo.max.online;

import java.io.Serializable;
import java.util.List;

/**
 * 回显前端限流集合数据POJO
 * <AUTHOR>
 * @date 2024年12月26日 15:53
 */
public class EchoLimitRuleConfig implements Serializable{
    private static final long serialVersionUID = 3071405142793680069L;

    /**限流规则集合*/
    private List<FlowRuleConfig> rules;

    /**主开关 0关闭 1开启*/
    private Integer mainSwitch;

    public List<FlowRuleConfig> getRules() { return rules; }

    public void setRules(List<FlowRuleConfig> rules) { this.rules = rules; }

    public Integer getMainSwitch() { return mainSwitch; }

    public void setMainSwitch(Integer mainSwitch) { this.mainSwitch = mainSwitch; }

    @Override
    public String toString() {
        return "EchoLimitRuleConfig{" +
                "rules=" + rules +
                ", mainSwitch=" + mainSwitch +
                '}';
    }
}
