package com.zxy.product.system.domain.vo;

import java.io.Serializable;

/**
 * 系统配置VO
 * <AUTHOR>
 * @date 2024年11月19日 11:32
 */
public class RuleConfigVO implements Serializable {
    private static final long serialVersionUID = -4322681193644631313L;

    /**系统配置主键*/
    private String id;

    /**系统配置组织Id*/
    private String organizationId;

    /**系统配置Key*/
    private String key;

    /**系统配置value*/
    private String value;

    /**系统配置类型（1常量 2JSON字符串）*/
    private Integer type;

    /**系统配置状态 0禁用 1启用*/
    private Integer status;

    /**系统配置排序号*/
    private Integer sequence;

    /**系统配置描述*/
    private String desc;

    public String getId() { return id; }

    public void setId(String id) { this.id = id; }

    public String getOrganizationId() { return organizationId; }

    public void setOrganizationId(String organizationId) { this.organizationId = organizationId; }

    public String getKey() { return key; }

    public void setKey(String key) { this.key = key; }

    public String getValue() { return value; }

    public void setValue(String value) { this.value = value; }

    public Integer getType() { return type; }

    public void setType(Integer type) { this.type = type; }

    public Integer getStatus() { return status; }

    public void setStatus(Integer status) { this.status = status; }

    public Integer getSequence() { return sequence; }

    public void setSequence(Integer sequence) { this.sequence = sequence; }

    public String getDesc() { return desc; }

    public void setDesc(String desc) { this.desc = desc; }

    @Override
    public String toString() {
        return "RuleConfigVO{" +
                "id='" + id + '\'' +
                ", organizationId='" + organizationId + '\'' +
                ", key='" + key + '\'' +
                ", value='" + value + '\'' +
                ", type=" + type +
                ", status=" + status +
                ", sequence=" + sequence +
                ", desc='" + desc + '\'' +
                '}';
    }
}
