package com.zxy.product.system.util;

import org.apache.http.HttpEntity;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.ContentType;
import org.apache.http.entity.FileEntity;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.impl.conn.PoolingHttpClientConnectionManager;
import org.apache.http.message.BasicNameValuePair;
import org.apache.http.util.EntityUtils;

import java.io.File;
import java.io.UnsupportedEncodingException;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Set;


public class HttpClientUtil {
    private static final CloseableHttpClient client;
    public static final String UTF8 = "UTF-8";
    public static final String GBK = "GBK";
    public static final String GB2312 = "GB2312";

    public HttpClientUtil() {
    }

    public static String httpGetCustomize(String url, Map<String, String> headers, Map<String, String> params, HttpClientUtil.RequestConfigType requestConfigType) {
        Iterator<Entry<String, String>> response;
        if(params != null) {
            StringBuilder get = (new StringBuilder(url)).append('?');
            response = params.entrySet().iterator();

            while(response.hasNext()) {
                Entry<String, String> e = response.next();
                get.append(e.getKey()).append('=').append(e.getValue()).append('&');
            }

            url = get.toString();
        }

        HttpGet get1 = new HttpGet(url);
        get1.setConfig(requestConfigType.requestConfig);
        response = null;
        get1.addHeader("Content-Type", "text/html;charset=UTF-8");
        Iterator<String> ite;
        if(headers != null) {
            Set<String> e1 = headers.keySet();
            ite = e1.iterator();

            while(ite.hasNext()) {
                String headerKey = ite.next();
                get1.addHeader(headerKey, headers.get(headerKey));
            }
        }

        String response1;
        try {
            HttpEntity e2 = client.execute(get1).getEntity();
            response1 = EntityUtils.toString(e2, "UTF-8");
        } catch (Exception var12) {
            throw new RuntimeException("http request failed to " + url, var12);
        } finally {
            get1.releaseConnection();
        }
        return response1;
    }

    public static String httpPost(String url, Map<String, String> headers, Map<String, String> params) {
        return httpPostCustomize(url, headers, params, HttpClientUtil.RequestConfigType.TIMEOUT_10000);
    }

    @SuppressWarnings("deprecation")
    public static String httpPostCustomize(String url, Map<String, String> headers, Map<String, String> params, HttpClientUtil.RequestConfigType requestConfigType) {
        HttpPost post = new HttpPost(url);
        post.setConfig(requestConfigType.requestConfig);
        ArrayList<BasicNameValuePair> httpParams = null;
        Iterator<Entry<String, String>> response;
        if(params != null && !params.isEmpty()) {
            httpParams = new ArrayList<BasicNameValuePair>(params.size());
            response = params.entrySet().iterator();

            Entry<String, String> e;
            while(response.hasNext()) {
                e = response.next();
                String k = e.getKey();
                String v = e.getValue();
                if(v == null) {
                    httpParams.add(new BasicNameValuePair(k, (String)null));
                } else {
                    httpParams.add(new BasicNameValuePair(k, v));
                }
            }

            if(headers != null) {
                response = headers.entrySet().iterator();

                while(response.hasNext()) {
                    e = response.next();
                    post.addHeader(e.getKey(), e.getValue());
                }
            }

            try {
                post.setEntity(new UrlEncodedFormEntity(httpParams, "UTF-8"));
                post.getParams().setParameter("http.protocol.cookie-policy", "compatibility");
            } catch (UnsupportedEncodingException var16) {
                throw new RuntimeException("UTF-8 is not surportted", var16);
            }
        }

        response = null;

        String response1;
        try {
            HttpEntity e1 = client.execute(post).getEntity();
            response1 = EntityUtils.toString(e1, "UTF-8");
        } catch (Exception var14) {
            throw new RuntimeException("error post data to " + url + " error message：" + var14.getMessage(), var14);
        } finally {
            post.releaseConnection();
        }

        return response1;
    }

    public static String httpPost(String url, Map<String, String> headers, String jsonString) {
        return httpPostCustomize(url, headers, jsonString, HttpClientUtil.RequestConfigType.TIMEOUT_10000);
    }

    public static String httpPostCustomize(String url, Map<String, String> headers, String jsonString, HttpClientUtil.RequestConfigType requestConfigType) {
        HttpPost post = new HttpPost(url);
        post.setConfig(requestConfigType.requestConfig);
        Iterator<Entry<String, String>> response;
        if(headers != null) {
            response = headers.entrySet().iterator();

            while(response.hasNext()) {
                Entry<String, String> e = response.next();
                post.addHeader(e.getKey(), e.getValue());
            }
        }

        if(jsonString != null) {
            StringEntity response1 = new StringEntity(jsonString, ContentType.APPLICATION_JSON);
            post.setEntity(response1);
        }

        response = null;

        String response2;
        try {
            HttpEntity e1 = client.execute(post).getEntity();
            response2 = EntityUtils.toString(e1, "UTF-8");
        } catch (Exception var10) {
            throw new RuntimeException("error post data to " + url, var10);
        } finally {
            post.releaseConnection();
        }

        return response2;
    }

    public static String httpGet(String url, Map<String, String> values) {
        Iterator<Entry<String, String>> response;
        if(values != null) {
            StringBuilder get = (new StringBuilder(url)).append('?');
            response = values.entrySet().iterator();

            while(response.hasNext()) {
                Entry<String, String> e = response.next();
                get.append(e.getKey()).append('=').append(e.getValue()).append('&');
            }

            url = get.toString();
        }

        HttpGet get1 = new HttpGet(url);
        response = null;
        get1.addHeader("Content-Type", "text/html;charset=UTF-8");
        get1.setConfig(HttpClientUtil.RequestConfigType.TIMEOUT_10000.requestConfig);
        Object var5;
        try {
            HttpEntity e1 = client.execute(get1).getEntity();
            String response1 = EntityUtils.toString(e1, "UTF-8");
            return response1;
        } catch (Exception var9) {
            var5 = null;
        } finally {
            get1.releaseConnection();
        }

        return (String)var5;
    }

    @SuppressWarnings("deprecation")
    public static String uploadFile(String url, File targetFile, Map<String, String> headers, Map<String, String> params) {
        FileEntity entity = new FileEntity(targetFile, ContentType.create("text/plain", "UTF-8"));
        HttpPost post = new HttpPost(url);
        post.setHeader("Content-Type", "text/html;charset=UTF-8");
        post.setConfig(HttpClientUtil.RequestConfigType.TIMEOUT_10000.requestConfig);
        ArrayList<BasicNameValuePair> httpParams = null;
        Set<String> e;
        Iterator<String> ite;
        String headerKey;
        if(params != null) {
            httpParams = new ArrayList<BasicNameValuePair>(params.size());
            e = params.keySet();
            ite = e.iterator();

            while(ite.hasNext()) {
                headerKey = ite.next();
                String value = params.get(headerKey);
                if(value != null) {
                    BasicNameValuePair pair = new BasicNameValuePair(headerKey, value);
                    httpParams.add(pair);
                }
            }
        }

        if(headers != null) {
            e = headers.keySet();
            ite = e.iterator();

            while(ite.hasNext()) {
                headerKey = ite.next();
                post.addHeader(headerKey, headers.get(headerKey));
            }
        }

        try {
            post.setEntity(new UrlEncodedFormEntity(httpParams, "UTF-8"));
            post.getParams().setParameter("http.protocol.cookie-policy", "compatibility");
        } catch (UnsupportedEncodingException var16) {
            throw new RuntimeException("UTF-8 is not surportted", var16);
        }

        post.setEntity(entity);

        String ite1;
        try {
            HttpEntity e1 = client.execute(post).getEntity();
            ite1 = EntityUtils.toString(e1, "UTF-8");
        } catch (Exception var17) {
            throw new RuntimeException("http request failed to " + url, var17);
        } finally {
            post.releaseConnection();
        }

        return ite1;
    }

    public static void main(String[] args) {
        for(int i = 0; i < 1; ++i) {
            String resp = httpGet("http://localhost:8080/nh_zhiyezhe-webapp", null);
            System.out.println(resp);
        }

    }

    static {
        PoolingHttpClientConnectionManager connManager = new PoolingHttpClientConnectionManager();
        connManager.setMaxTotal(100);
        connManager.setDefaultMaxPerRoute(10);
        client = HttpClients.custom().setConnectionManager(connManager).build();
    }

    public static enum RequestConfigType {
        TIMEOUT_60000('\uea60', '\uea60', '\uea60'),
        TIMEOUT_10000(10000, 10000, 10000),
        TIMEOUT_5000(5000, 5000, 5000),
        TIMEOUT_2000(2000, 2000, 2000),
        TIMEOUT_500(500, 500, 500);

        private RequestConfig requestConfig;

        private RequestConfigType(int socketTimeout, int connectTimeout, int requestTimeout) {
            this.requestConfig = RequestConfig.custom().setSocketTimeout(socketTimeout).setConnectTimeout(connectTimeout).setConnectionRequestTimeout(requestTimeout).build();
        }
    }
}