package com.zxy.product.system.domain.vo;

import java.io.Serializable;

/**
 * 重写：首页精选内容VO
 *
 * <AUTHOR>
 * @date 2024年03月17日 16:49
 */
public class HomeAdvertisementVO implements Serializable {
    private static final long serialVersionUID=1L;

    /**首页精选内容题目*/
    private String  title;

    /**首页精选内容PC图片*/
    private String pcImage;

    /**首页精选内容业务Id*/
    private String businessId;

    /**首页精选内容PC图片路径*/
    private String pcImagePath;

    private String id;

    private Integer linkType;

    private Integer businessType;

    private String linkAddress;

    public String getId() { return id; }

    public void setId(String id) { this.id = id; }

    public Integer getLinkType() { return linkType; }

    public void setLinkType(Integer linkType) { this.linkType = linkType; }

    public Integer getBusinessType() { return businessType; }

    public void setBusinessType(Integer businessType) { this.businessType = businessType; }

    public String getLinkAddress() { return linkAddress; }

    public void setLinkAddress(String linkAddress) { this.linkAddress = linkAddress; }

    public String getTitle() { return title; }

    public void setTitle(String title) { this.title = title; }

    public String getPcImage() { return pcImage; }

    public void setPcImage(String pcImage) { this.pcImage = pcImage; }

    public String getBusinessId() { return businessId; }

    public void setBusinessId(String businessId) { this.businessId = businessId; }

    public String getPcImagePath() { return pcImagePath; }

    public void setPcImagePath(String pcImagePath) { this.pcImagePath = pcImagePath; }

    @Override
    public String toString() {
        return "HomeAdvertisementVO{" +
                "title='" + title + '\'' +
                ", pcImage='" + pcImage + '\'' +
                ", businessId='" + businessId + '\'' +
                ", pcImagePath='" + pcImagePath + '\'' +
                ", id='" + id + '\'' +
                ", linkType='" + linkType + '\'' +
                ", businessType=" + businessType +
                ", linkAddress='" + linkAddress + '\'' +
                '}';
    }
}
