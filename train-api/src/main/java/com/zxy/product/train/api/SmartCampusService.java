package com.zxy.product.train.api;

import com.zxy.common.base.annotation.RemoteService;
import org.jooq.Condition;
import org.jooq.Field;
import org.jooq.Table;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;

@RemoteService(timeout=700000)
public interface SmartCampusService {


    @Transactional
    Map<String,String> register(String classId, String memberId, Long registerTime);

    @Transactional
    Map<String,String> studentAttendance(String classId, String memberId, Long attendanceTime);

    @Transactional
    Map<String,Object> getJumpLink(Long currentTimestamp,String classId,String userName,boolean bearerNetwork);

    @Transactional
    void pushOverallSatisfaction(List<String> ids);

    @Transactional
    void fieldCompensation(List<String> ids);
    @Transactional
    void fieldCompensation2(List<String> ids);

    /**
     * 班级结束同步到智慧教务系统
     *
     * @param classId 班级ID
     * @return 同步结果
     */
    @Transactional(readOnly = true)
    Map<String, Object> finishClass(String classId);

}
