/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.train.jooq.tables.pojos;


import com.zxy.common.base.entity.BaseEntity;
import com.zxy.product.train.jooq.tables.interfaces.IProject;

import java.sql.Timestamp;

import javax.annotation.Generated;


/**
 * This class is generated by jOOQ.
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class ProjectEntity extends BaseEntity implements IProject {

    private static final long serialVersionUID = 1L;

    private String    name;
    private String    code;
    private String    contactMemberId;
    private String    organizationId;
    private String    contactPhone;
    private String    contactEmail;
    private Integer   year;
    private Integer   month;
    private Integer   amount;
    private String    object;
    private Integer   days;
    private String    typeId;
    private String    address;
    private Integer   status;
    private String    createMember;
    private Integer   deleteFlag;
    private Integer   studentStatus;
    private String    cost;
    private String    surveyType;
    private String    target;
    private String    temp;
    private Integer   isPartyCadre;
    private Timestamp modifyDate;
    private String    reservationMember;
    private Long      reservationTime;
    private Integer   isManualFinish;

    public ProjectEntity() {}

    public ProjectEntity(ProjectEntity value) {
        this.name = value.name;
        this.code = value.code;
        this.contactMemberId = value.contactMemberId;
        this.organizationId = value.organizationId;
        this.contactPhone = value.contactPhone;
        this.contactEmail = value.contactEmail;
        this.year = value.year;
        this.month = value.month;
        this.amount = value.amount;
        this.object = value.object;
        this.days = value.days;
        this.typeId = value.typeId;
        this.address = value.address;
        this.status = value.status;
        this.createMember = value.createMember;
        this.deleteFlag = value.deleteFlag;
        this.studentStatus = value.studentStatus;
        this.cost = value.cost;
        this.surveyType = value.surveyType;
        this.target = value.target;
        this.temp = value.temp;
        this.isPartyCadre = value.isPartyCadre;
        this.modifyDate = value.modifyDate;
        this.reservationMember = value.reservationMember;
        this.reservationTime = value.reservationTime;
        this.isManualFinish = value.isManualFinish;
    }

    public ProjectEntity(
        String    id,
        String    name,
        String    code,
        String    contactMemberId,
        String    organizationId,
        String    contactPhone,
        String    contactEmail,
        Integer   year,
        Integer   month,
        Integer   amount,
        String    object,
        Integer   days,
        String    typeId,
        String    address,
        Integer   status,
        Long      createTime,
        String    createMember,
        Integer   deleteFlag,
        Integer   studentStatus,
        String    cost,
        String    surveyType,
        String    target,
        String    temp,
        Integer   isPartyCadre,
        Timestamp modifyDate,
        String    reservationMember,
        Long      reservationTime,
        Integer   isManualFinish
    ) {
        super.setId(id);
        this.name = name;
        this.code = code;
        this.contactMemberId = contactMemberId;
        this.organizationId = organizationId;
        this.contactPhone = contactPhone;
        this.contactEmail = contactEmail;
        this.year = year;
        this.month = month;
        this.amount = amount;
        this.object = object;
        this.days = days;
        this.typeId = typeId;
        this.address = address;
        this.status = status;
        super.setCreateTime(createTime);
        this.createMember = createMember;
        this.deleteFlag = deleteFlag;
        this.studentStatus = studentStatus;
        this.cost = cost;
        this.surveyType = surveyType;
        this.target = target;
        this.temp = temp;
        this.isPartyCadre = isPartyCadre;
        this.modifyDate = modifyDate;
        this.reservationMember = reservationMember;
        this.reservationTime = reservationTime;
        this.isManualFinish = isManualFinish;
    }

    @Override
    public String getId() {
        return super.getId();
    }

    @Override
    public void setId(String id) {
        super.setId(id);
    }

    @Override
    public String getName() {
        return this.name;
    }

    @Override
    public void setName(String name) {
        this.name = name;
    }

    @Override
    public String getCode() {
        return this.code;
    }

    @Override
    public void setCode(String code) {
        this.code = code;
    }

    @Override
    public String getContactMemberId() {
        return this.contactMemberId;
    }

    @Override
    public void setContactMemberId(String contactMemberId) {
        this.contactMemberId = contactMemberId;
    }

    @Override
    public String getOrganizationId() {
        return this.organizationId;
    }

    @Override
    public void setOrganizationId(String organizationId) {
        this.organizationId = organizationId;
    }

    @Override
    public String getContactPhone() {
        return this.contactPhone;
    }

    @Override
    public void setContactPhone(String contactPhone) {
        this.contactPhone = contactPhone;
    }

    @Override
    public String getContactEmail() {
        return this.contactEmail;
    }

    @Override
    public void setContactEmail(String contactEmail) {
        this.contactEmail = contactEmail;
    }

    @Override
    public Integer getYear() {
        return this.year;
    }

    @Override
    public void setYear(Integer year) {
        this.year = year;
    }

    @Override
    public Integer getMonth() {
        return this.month;
    }

    @Override
    public void setMonth(Integer month) {
        this.month = month;
    }

    @Override
    public Integer getAmount() {
        return this.amount;
    }

    @Override
    public void setAmount(Integer amount) {
        this.amount = amount;
    }

    @Override
    public String getObject() {
        return this.object;
    }

    @Override
    public void setObject(String object) {
        this.object = object;
    }

    @Override
    public Integer getDays() {
        return this.days;
    }

    @Override
    public void setDays(Integer days) {
        this.days = days;
    }

    @Override
    public String getTypeId() {
        return this.typeId;
    }

    @Override
    public void setTypeId(String typeId) {
        this.typeId = typeId;
    }

    @Override
    public String getAddress() {
        return this.address;
    }

    @Override
    public void setAddress(String address) {
        this.address = address;
    }

    @Override
    public Integer getStatus() {
        return this.status;
    }

    @Override
    public void setStatus(Integer status) {
        this.status = status;
    }

    @Override
    public Long getCreateTime() {
        return super.getCreateTime();
    }

    @Override
    public void setCreateTime(Long createTime) {
        super.setCreateTime(createTime);
    }

    @Override
    public String getCreateMember() {
        return this.createMember;
    }

    @Override
    public void setCreateMember(String createMember) {
        this.createMember = createMember;
    }

    @Override
    public Integer getDeleteFlag() {
        return this.deleteFlag;
    }

    @Override
    public void setDeleteFlag(Integer deleteFlag) {
        this.deleteFlag = deleteFlag;
    }

    @Override
    public Integer getStudentStatus() {
        return this.studentStatus;
    }

    @Override
    public void setStudentStatus(Integer studentStatus) {
        this.studentStatus = studentStatus;
    }

    @Override
    public String getCost() {
        return this.cost;
    }

    @Override
    public void setCost(String cost) {
        this.cost = cost;
    }

    @Override
    public String getSurveyType() {
        return this.surveyType;
    }

    @Override
    public void setSurveyType(String surveyType) {
        this.surveyType = surveyType;
    }

    @Override
    public String getTarget() {
        return this.target;
    }

    @Override
    public void setTarget(String target) {
        this.target = target;
    }

    @Override
    public String getTemp() {
        return this.temp;
    }

    @Override
    public void setTemp(String temp) {
        this.temp = temp;
    }

    @Override
    public Integer getIsPartyCadre() {
        return this.isPartyCadre;
    }

    @Override
    public void setIsPartyCadre(Integer isPartyCadre) {
        this.isPartyCadre = isPartyCadre;
    }

    @Override
    public Timestamp getModifyDate() {
        return this.modifyDate;
    }

    @Override
    public void setModifyDate(Timestamp modifyDate) {
        this.modifyDate = modifyDate;
    }

    @Override
    public String getReservationMember() {
        return this.reservationMember;
    }

    @Override
    public void setReservationMember(String reservationMember) {
        this.reservationMember = reservationMember;
    }

    @Override
    public Long getReservationTime() {
        return this.reservationTime;
    }

    @Override
    public void setReservationTime(Long reservationTime) {
        this.reservationTime = reservationTime;
    }

    @Override
    public Integer getIsManualFinish() {
        return this.isManualFinish;
    }

    @Override
    public void setIsManualFinish(Integer isManualFinish) {
        this.isManualFinish = isManualFinish;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder("ProjectEntity (");

        sb.append(getId());
        sb.append(", ").append(name);
        sb.append(", ").append(code);
        sb.append(", ").append(contactMemberId);
        sb.append(", ").append(organizationId);
        sb.append(", ").append(contactPhone);
        sb.append(", ").append(contactEmail);
        sb.append(", ").append(year);
        sb.append(", ").append(month);
        sb.append(", ").append(amount);
        sb.append(", ").append(object);
        sb.append(", ").append(days);
        sb.append(", ").append(typeId);
        sb.append(", ").append(address);
        sb.append(", ").append(status);
        sb.append(", ").append(getCreateTime());
        sb.append(", ").append(createMember);
        sb.append(", ").append(deleteFlag);
        sb.append(", ").append(studentStatus);
        sb.append(", ").append(cost);
        sb.append(", ").append(surveyType);
        sb.append(", ").append(target);
        sb.append(", ").append(temp);
        sb.append(", ").append(isPartyCadre);
        sb.append(", ").append(modifyDate);
        sb.append(", ").append(reservationMember);
        sb.append(", ").append(reservationTime);
        sb.append(", ").append(isManualFinish);

        sb.append(")");
        return sb.toString();
    }

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public void from(IProject from) {
        setId(from.getId());
        setName(from.getName());
        setCode(from.getCode());
        setContactMemberId(from.getContactMemberId());
        setOrganizationId(from.getOrganizationId());
        setContactPhone(from.getContactPhone());
        setContactEmail(from.getContactEmail());
        setYear(from.getYear());
        setMonth(from.getMonth());
        setAmount(from.getAmount());
        setObject(from.getObject());
        setDays(from.getDays());
        setTypeId(from.getTypeId());
        setAddress(from.getAddress());
        setStatus(from.getStatus());
        setCreateTime(from.getCreateTime());
        setCreateMember(from.getCreateMember());
        setDeleteFlag(from.getDeleteFlag());
        setStudentStatus(from.getStudentStatus());
        setCost(from.getCost());
        setSurveyType(from.getSurveyType());
        setTarget(from.getTarget());
        setTemp(from.getTemp());
        setIsPartyCadre(from.getIsPartyCadre());
        setModifyDate(from.getModifyDate());
        setReservationMember(from.getReservationMember());
        setReservationTime(from.getReservationTime());
        setIsManualFinish(from.getIsManualFinish());
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public <E extends IProject> E into(E into) {
        into.from(this);
        return into;
    }

    public void forInsert() {
        this.setId(java.util.UUID.randomUUID().toString());
        this.setCreateTime(System.currentTimeMillis());
    }

    public static final <E extends ProjectEntity> org.jooq.RecordMapper<? extends org.jooq.Record, E> createMapper(Class<E> type) {
        return new org.jooq.RecordMapper<org.jooq.Record, E>() {
            @Override
            public E map(org.jooq.Record record) {
                com.zxy.product.train.jooq.tables.records.ProjectRecord r = new com.zxy.product.train.jooq.tables.records.ProjectRecord();
                org.jooq.Row row = record.fieldsRow();
                row.fieldStream().filter(f -> { return f.toString().equals(com.zxy.product.train.jooq.tables.Project.PROJECT.ID.toString()); }).findAny().ifPresent(field -> {
                    r.setValue(com.zxy.product.train.jooq.tables.Project.PROJECT.ID, record.getValue(com.zxy.product.train.jooq.tables.Project.PROJECT.ID));});
                row.fieldStream().filter(f -> { return f.toString().equals(com.zxy.product.train.jooq.tables.Project.PROJECT.NAME.toString()); }).findAny().ifPresent(field -> {
                    r.setValue(com.zxy.product.train.jooq.tables.Project.PROJECT.NAME, record.getValue(com.zxy.product.train.jooq.tables.Project.PROJECT.NAME));});
                row.fieldStream().filter(f -> { return f.toString().equals(com.zxy.product.train.jooq.tables.Project.PROJECT.CODE.toString()); }).findAny().ifPresent(field -> {
                    r.setValue(com.zxy.product.train.jooq.tables.Project.PROJECT.CODE, record.getValue(com.zxy.product.train.jooq.tables.Project.PROJECT.CODE));});
                row.fieldStream().filter(f -> { return f.toString().equals(com.zxy.product.train.jooq.tables.Project.PROJECT.CONTACT_MEMBER_ID.toString()); }).findAny().ifPresent(field -> {
                    r.setValue(com.zxy.product.train.jooq.tables.Project.PROJECT.CONTACT_MEMBER_ID, record.getValue(com.zxy.product.train.jooq.tables.Project.PROJECT.CONTACT_MEMBER_ID));});
                row.fieldStream().filter(f -> { return f.toString().equals(com.zxy.product.train.jooq.tables.Project.PROJECT.ORGANIZATION_ID.toString()); }).findAny().ifPresent(field -> {
                    r.setValue(com.zxy.product.train.jooq.tables.Project.PROJECT.ORGANIZATION_ID, record.getValue(com.zxy.product.train.jooq.tables.Project.PROJECT.ORGANIZATION_ID));});
                row.fieldStream().filter(f -> { return f.toString().equals(com.zxy.product.train.jooq.tables.Project.PROJECT.CONTACT_PHONE.toString()); }).findAny().ifPresent(field -> {
                    r.setValue(com.zxy.product.train.jooq.tables.Project.PROJECT.CONTACT_PHONE, record.getValue(com.zxy.product.train.jooq.tables.Project.PROJECT.CONTACT_PHONE));});
                row.fieldStream().filter(f -> { return f.toString().equals(com.zxy.product.train.jooq.tables.Project.PROJECT.CONTACT_EMAIL.toString()); }).findAny().ifPresent(field -> {
                    r.setValue(com.zxy.product.train.jooq.tables.Project.PROJECT.CONTACT_EMAIL, record.getValue(com.zxy.product.train.jooq.tables.Project.PROJECT.CONTACT_EMAIL));});
                row.fieldStream().filter(f -> { return f.toString().equals(com.zxy.product.train.jooq.tables.Project.PROJECT.YEAR.toString()); }).findAny().ifPresent(field -> {
                    r.setValue(com.zxy.product.train.jooq.tables.Project.PROJECT.YEAR, record.getValue(com.zxy.product.train.jooq.tables.Project.PROJECT.YEAR));});
                row.fieldStream().filter(f -> { return f.toString().equals(com.zxy.product.train.jooq.tables.Project.PROJECT.MONTH.toString()); }).findAny().ifPresent(field -> {
                    r.setValue(com.zxy.product.train.jooq.tables.Project.PROJECT.MONTH, record.getValue(com.zxy.product.train.jooq.tables.Project.PROJECT.MONTH));});
                row.fieldStream().filter(f -> { return f.toString().equals(com.zxy.product.train.jooq.tables.Project.PROJECT.AMOUNT.toString()); }).findAny().ifPresent(field -> {
                    r.setValue(com.zxy.product.train.jooq.tables.Project.PROJECT.AMOUNT, record.getValue(com.zxy.product.train.jooq.tables.Project.PROJECT.AMOUNT));});
                row.fieldStream().filter(f -> { return f.toString().equals(com.zxy.product.train.jooq.tables.Project.PROJECT.OBJECT.toString()); }).findAny().ifPresent(field -> {
                    r.setValue(com.zxy.product.train.jooq.tables.Project.PROJECT.OBJECT, record.getValue(com.zxy.product.train.jooq.tables.Project.PROJECT.OBJECT));});
                row.fieldStream().filter(f -> { return f.toString().equals(com.zxy.product.train.jooq.tables.Project.PROJECT.DAYS.toString()); }).findAny().ifPresent(field -> {
                    r.setValue(com.zxy.product.train.jooq.tables.Project.PROJECT.DAYS, record.getValue(com.zxy.product.train.jooq.tables.Project.PROJECT.DAYS));});
                row.fieldStream().filter(f -> { return f.toString().equals(com.zxy.product.train.jooq.tables.Project.PROJECT.TYPE_ID.toString()); }).findAny().ifPresent(field -> {
                    r.setValue(com.zxy.product.train.jooq.tables.Project.PROJECT.TYPE_ID, record.getValue(com.zxy.product.train.jooq.tables.Project.PROJECT.TYPE_ID));});
                row.fieldStream().filter(f -> { return f.toString().equals(com.zxy.product.train.jooq.tables.Project.PROJECT.ADDRESS.toString()); }).findAny().ifPresent(field -> {
                    r.setValue(com.zxy.product.train.jooq.tables.Project.PROJECT.ADDRESS, record.getValue(com.zxy.product.train.jooq.tables.Project.PROJECT.ADDRESS));});
                row.fieldStream().filter(f -> { return f.toString().equals(com.zxy.product.train.jooq.tables.Project.PROJECT.STATUS.toString()); }).findAny().ifPresent(field -> {
                    r.setValue(com.zxy.product.train.jooq.tables.Project.PROJECT.STATUS, record.getValue(com.zxy.product.train.jooq.tables.Project.PROJECT.STATUS));});
                row.fieldStream().filter(f -> { return f.toString().equals(com.zxy.product.train.jooq.tables.Project.PROJECT.CREATE_TIME.toString()); }).findAny().ifPresent(field -> {
                    r.setValue(com.zxy.product.train.jooq.tables.Project.PROJECT.CREATE_TIME, record.getValue(com.zxy.product.train.jooq.tables.Project.PROJECT.CREATE_TIME));});
                row.fieldStream().filter(f -> { return f.toString().equals(com.zxy.product.train.jooq.tables.Project.PROJECT.CREATE_MEMBER.toString()); }).findAny().ifPresent(field -> {
                    r.setValue(com.zxy.product.train.jooq.tables.Project.PROJECT.CREATE_MEMBER, record.getValue(com.zxy.product.train.jooq.tables.Project.PROJECT.CREATE_MEMBER));});
                row.fieldStream().filter(f -> { return f.toString().equals(com.zxy.product.train.jooq.tables.Project.PROJECT.DELETE_FLAG.toString()); }).findAny().ifPresent(field -> {
                    r.setValue(com.zxy.product.train.jooq.tables.Project.PROJECT.DELETE_FLAG, record.getValue(com.zxy.product.train.jooq.tables.Project.PROJECT.DELETE_FLAG));});
                row.fieldStream().filter(f -> { return f.toString().equals(com.zxy.product.train.jooq.tables.Project.PROJECT.STUDENT_STATUS.toString()); }).findAny().ifPresent(field -> {
                    r.setValue(com.zxy.product.train.jooq.tables.Project.PROJECT.STUDENT_STATUS, record.getValue(com.zxy.product.train.jooq.tables.Project.PROJECT.STUDENT_STATUS));});
                row.fieldStream().filter(f -> { return f.toString().equals(com.zxy.product.train.jooq.tables.Project.PROJECT.COST.toString()); }).findAny().ifPresent(field -> {
                    r.setValue(com.zxy.product.train.jooq.tables.Project.PROJECT.COST, record.getValue(com.zxy.product.train.jooq.tables.Project.PROJECT.COST));});
                row.fieldStream().filter(f -> { return f.toString().equals(com.zxy.product.train.jooq.tables.Project.PROJECT.SURVEY_TYPE.toString()); }).findAny().ifPresent(field -> {
                    r.setValue(com.zxy.product.train.jooq.tables.Project.PROJECT.SURVEY_TYPE, record.getValue(com.zxy.product.train.jooq.tables.Project.PROJECT.SURVEY_TYPE));});
                row.fieldStream().filter(f -> { return f.toString().equals(com.zxy.product.train.jooq.tables.Project.PROJECT.TARGET.toString()); }).findAny().ifPresent(field -> {
                    r.setValue(com.zxy.product.train.jooq.tables.Project.PROJECT.TARGET, record.getValue(com.zxy.product.train.jooq.tables.Project.PROJECT.TARGET));});
                row.fieldStream().filter(f -> { return f.toString().equals(com.zxy.product.train.jooq.tables.Project.PROJECT.TEMP.toString()); }).findAny().ifPresent(field -> {
                    r.setValue(com.zxy.product.train.jooq.tables.Project.PROJECT.TEMP, record.getValue(com.zxy.product.train.jooq.tables.Project.PROJECT.TEMP));});
                row.fieldStream().filter(f -> { return f.toString().equals(com.zxy.product.train.jooq.tables.Project.PROJECT.IS_PARTY_CADRE.toString()); }).findAny().ifPresent(field -> {
                    r.setValue(com.zxy.product.train.jooq.tables.Project.PROJECT.IS_PARTY_CADRE, record.getValue(com.zxy.product.train.jooq.tables.Project.PROJECT.IS_PARTY_CADRE));});
                row.fieldStream().filter(f -> { return f.toString().equals(com.zxy.product.train.jooq.tables.Project.PROJECT.MODIFY_DATE.toString()); }).findAny().ifPresent(field -> {
                    r.setValue(com.zxy.product.train.jooq.tables.Project.PROJECT.MODIFY_DATE, record.getValue(com.zxy.product.train.jooq.tables.Project.PROJECT.MODIFY_DATE));});
                row.fieldStream().filter(f -> { return f.toString().equals(com.zxy.product.train.jooq.tables.Project.PROJECT.RESERVATION_MEMBER.toString()); }).findAny().ifPresent(field -> {
                    r.setValue(com.zxy.product.train.jooq.tables.Project.PROJECT.RESERVATION_MEMBER, record.getValue(com.zxy.product.train.jooq.tables.Project.PROJECT.RESERVATION_MEMBER));});
                row.fieldStream().filter(f -> { return f.toString().equals(com.zxy.product.train.jooq.tables.Project.PROJECT.RESERVATION_TIME.toString()); }).findAny().ifPresent(field -> {
                    r.setValue(com.zxy.product.train.jooq.tables.Project.PROJECT.RESERVATION_TIME, record.getValue(com.zxy.product.train.jooq.tables.Project.PROJECT.RESERVATION_TIME));});
                try {
                    E pojo = type.newInstance();
                    pojo.from(r);
                    return pojo;
                } catch (Exception e) {
                    e.printStackTrace(); // ignored
                }
                return null;
            }
        };
    }}
