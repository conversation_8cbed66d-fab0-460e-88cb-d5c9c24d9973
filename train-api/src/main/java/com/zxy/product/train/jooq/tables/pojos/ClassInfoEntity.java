/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.train.jooq.tables.pojos;


import com.zxy.common.base.entity.BaseEntity;
import com.zxy.product.train.jooq.tables.interfaces.IClassInfo;

import javax.annotation.Generated;
import java.sql.Timestamp;


/**
 * This class is generated by jOOQ.
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class ClassInfoEntity extends BaseEntity implements IClassInfo {

    private static final long serialVersionUID = 1L;

    private String    projectId;
    private String    classTeacherPhone;
    private String    classTeacher;
    private Long      arriveDate;
    private Long      returnDate;
    private Integer   isOutside;
    private String    surveyType;
    private String    target;
    private String    classInfoType;
    private String    studentType;
    private String    simpleType;
    private Integer   isPlan;
    private Integer   status;
    private Integer   confirm;
    private String    groupId;
    private String    shortName;
    private Integer   groupOrder;
    private String    createMember;
    private Integer   deleteFlag;
    private Integer   implementationYear;
    private Integer   implementationMonth;
    private Double    memberSatisfaction;
    private Double    classSatisfaction;
    private Integer   traineeNum;
    private Integer   submitNum;
    private Integer   notice;
    private Integer   resourceStatus;
    private Double    totalitySatisfied;
    private Double    courseSatisfied;
    private String    organizationId;
    private Integer   projectSource;
    private Integer   sort;
    private Integer   isOverproof;
    private Integer   fourDegreesSubmitNum;
    private Integer   abilitySubmitNum;
    private Integer   superiorLeadershipSubmitNum;
    private Integer   questionnaireStatus;
    private Integer   courseSalary;
    private Integer   specialClass;
    private Integer   classLevel;
    private Integer      view;
    private Timestamp modifyDate;

    public ClassInfoEntity() {}

    public ClassInfoEntity(ClassInfoEntity value) {
        this.projectId = value.projectId;
        this.classTeacherPhone = value.classTeacherPhone;
        this.classTeacher = value.classTeacher;
        this.arriveDate = value.arriveDate;
        this.returnDate = value.returnDate;
        this.isOutside = value.isOutside;
        this.surveyType = value.surveyType;
        this.target = value.target;
        this.classInfoType = value.classInfoType;
        this.studentType = value.studentType;
        this.simpleType = value.simpleType;
        this.isPlan = value.isPlan;
        this.status = value.status;
        this.confirm = value.confirm;
        this.groupId = value.groupId;
        this.shortName = value.shortName;
        this.groupOrder = value.groupOrder;
        this.createMember = value.createMember;
        this.deleteFlag = value.deleteFlag;
        this.implementationYear = value.implementationYear;
        this.implementationMonth = value.implementationMonth;
        this.memberSatisfaction = value.memberSatisfaction;
        this.classSatisfaction = value.classSatisfaction;
        this.traineeNum = value.traineeNum;
        this.submitNum = value.submitNum;
        this.notice = value.notice;
        this.resourceStatus = value.resourceStatus;
        this.totalitySatisfied = value.totalitySatisfied;
        this.courseSatisfied = value.courseSatisfied;
        this.organizationId = value.organizationId;
        this.projectSource = value.projectSource;
        this.sort = value.sort;
        this.isOverproof = value.isOverproof;
        this.fourDegreesSubmitNum = value.fourDegreesSubmitNum;
        this.abilitySubmitNum = value.abilitySubmitNum;
        this.superiorLeadershipSubmitNum = value.superiorLeadershipSubmitNum;
        this.questionnaireStatus = value.questionnaireStatus;
        this.courseSalary = value.courseSalary;
        this.specialClass = value.specialClass;
        this.classLevel = value.classLevel;
        this.view = value.view;
        this.modifyDate = value.modifyDate;
    }

    public ClassInfoEntity(
        String    id,
        String    projectId,
        String    classTeacherPhone,
        String    classTeacher,
        Long      arriveDate,
        Long      returnDate,
        Integer   isOutside,
        String    surveyType,
        String    target,
        String    classInfoType,
        String    studentType,
        String    simpleType,
        Integer   isPlan,
        Integer   status,
        Integer   confirm,
        String    groupId,
        String    shortName,
        Integer   groupOrder,
        Long      createTime,
        String    createMember,
        Integer   deleteFlag,
        Integer   implementationYear,
        Integer   implementationMonth,
        Double    memberSatisfaction,
        Double    classSatisfaction,
        Integer   traineeNum,
        Integer   submitNum,
        Integer   notice,
        Integer   resourceStatus,
        Double    totalitySatisfied,
        Double    courseSatisfied,
        String    organizationId,
        Integer   projectSource,
        Integer   sort,
        Integer   isOverproof,
        Integer   fourDegreesSubmitNum,
        Integer   abilitySubmitNum,
        Integer   superiorLeadershipSubmitNum,
        Integer   questionnaireStatus,
        Integer   courseSalary,
        Integer   specialClass,
        Integer   classLevel,
        Integer      view,
        Timestamp modifyDate
    ) {
        super.setId(id);
        this.projectId = projectId;
        this.classTeacherPhone = classTeacherPhone;
        this.classTeacher = classTeacher;
        this.arriveDate = arriveDate;
        this.returnDate = returnDate;
        this.isOutside = isOutside;
        this.surveyType = surveyType;
        this.target = target;
        this.classInfoType = classInfoType;
        this.studentType = studentType;
        this.simpleType = simpleType;
        this.isPlan = isPlan;
        this.status = status;
        this.confirm = confirm;
        this.groupId = groupId;
        this.shortName = shortName;
        this.groupOrder = groupOrder;
        super.setCreateTime(createTime);
        this.createMember = createMember;
        this.deleteFlag = deleteFlag;
        this.implementationYear = implementationYear;
        this.implementationMonth = implementationMonth;
        this.memberSatisfaction = memberSatisfaction;
        this.classSatisfaction = classSatisfaction;
        this.traineeNum = traineeNum;
        this.submitNum = submitNum;
        this.notice = notice;
        this.resourceStatus = resourceStatus;
        this.totalitySatisfied = totalitySatisfied;
        this.courseSatisfied = courseSatisfied;
        this.organizationId = organizationId;
        this.projectSource = projectSource;
        this.sort = sort;
        this.isOverproof = isOverproof;
        this.fourDegreesSubmitNum = fourDegreesSubmitNum;
        this.abilitySubmitNum = abilitySubmitNum;
        this.superiorLeadershipSubmitNum = superiorLeadershipSubmitNum;
        this.questionnaireStatus = questionnaireStatus;
        this.courseSalary = courseSalary;
        this.specialClass = specialClass;
        this.classLevel = classLevel;
        this.view = view;
        this.modifyDate = modifyDate;
    }

    @Override
    public String getId() {
        return super.getId();
    }

    @Override
    public void setId(String id) {
        super.setId(id);
    }

    @Override
    public String getProjectId() {
        return this.projectId;
    }

    @Override
    public void setProjectId(String projectId) {
        this.projectId = projectId;
    }

    @Override
    public String getClassTeacherPhone() {
        return this.classTeacherPhone;
    }

    @Override
    public void setClassTeacherPhone(String classTeacherPhone) {
        this.classTeacherPhone = classTeacherPhone;
    }

    @Override
    public String getClassTeacher() {
        return this.classTeacher;
    }

    @Override
    public void setClassTeacher(String classTeacher) {
        this.classTeacher = classTeacher;
    }

    @Override
    public Long getArriveDate() {
        return this.arriveDate;
    }

    @Override
    public void setArriveDate(Long arriveDate) {
        this.arriveDate = arriveDate;
    }

    @Override
    public Long getReturnDate() {
        return this.returnDate;
    }

    @Override
    public void setReturnDate(Long returnDate) {
        this.returnDate = returnDate;
    }

    @Override
    public Integer getIsOutside() {
        return this.isOutside;
    }

    @Override
    public void setIsOutside(Integer isOutside) {
        this.isOutside = isOutside;
    }

    @Override
    public String getSurveyType() {
        return this.surveyType;
    }

    @Override
    public void setSurveyType(String surveyType) {
        this.surveyType = surveyType;
    }

    @Override
    public String getTarget() {
        return this.target;
    }

    @Override
    public void setTarget(String target) {
        this.target = target;
    }

    @Override
    public String getClassInfoType() {
        return this.classInfoType;
    }

    @Override
    public void setClassInfoType(String classInfoType) {
        this.classInfoType = classInfoType;
    }

    @Override
    public String getStudentType() {
        return this.studentType;
    }

    @Override
    public void setStudentType(String studentType) {
        this.studentType = studentType;
    }

    @Override
    public String getSimpleType() {
        return this.simpleType;
    }

    @Override
    public void setSimpleType(String simpleType) {
        this.simpleType = simpleType;
    }

    @Override
    public Integer getIsPlan() {
        return this.isPlan;
    }

    @Override
    public void setIsPlan(Integer isPlan) {
        this.isPlan = isPlan;
    }

    @Override
    public Integer getStatus() {
        return this.status;
    }

    @Override
    public void setStatus(Integer status) {
        this.status = status;
    }

    @Override
    public Integer getConfirm() {
        return this.confirm;
    }

    @Override
    public void setConfirm(Integer confirm) {
        this.confirm = confirm;
    }

    @Override
    public String getGroupId() {
        return this.groupId;
    }

    @Override
    public void setGroupId(String groupId) {
        this.groupId = groupId;
    }

    @Override
    public String getShortName() {
        return this.shortName;
    }

    @Override
    public void setShortName(String shortName) {
        this.shortName = shortName;
    }

    @Override
    public Integer getGroupOrder() {
        return this.groupOrder;
    }

    @Override
    public void setGroupOrder(Integer groupOrder) {
        this.groupOrder = groupOrder;
    }

    @Override
    public Long getCreateTime() {
        return super.getCreateTime();
    }

    @Override
    public void setCreateTime(Long createTime) {
        super.setCreateTime(createTime);
    }

    @Override
    public String getCreateMember() {
        return this.createMember;
    }

    @Override
    public void setCreateMember(String createMember) {
        this.createMember = createMember;
    }

    @Override
    public Integer getDeleteFlag() {
        return this.deleteFlag;
    }

    @Override
    public void setDeleteFlag(Integer deleteFlag) {
        this.deleteFlag = deleteFlag;
    }

    @Override
    public Integer getImplementationYear() {
        return this.implementationYear;
    }

    @Override
    public void setImplementationYear(Integer implementationYear) {
        this.implementationYear = implementationYear;
    }

    @Override
    public Integer getImplementationMonth() {
        return this.implementationMonth;
    }

    @Override
    public void setImplementationMonth(Integer implementationMonth) {
        this.implementationMonth = implementationMonth;
    }

    @Override
    public Double getMemberSatisfaction() {
        return this.memberSatisfaction;
    }

    @Override
    public void setMemberSatisfaction(Double memberSatisfaction) {
        this.memberSatisfaction = memberSatisfaction;
    }

    @Override
    public Double getClassSatisfaction() {
        return this.classSatisfaction;
    }

    @Override
    public void setClassSatisfaction(Double classSatisfaction) {
        this.classSatisfaction = classSatisfaction;
    }

    @Override
    public Integer getTraineeNum() {
        return this.traineeNum;
    }

    @Override
    public void setTraineeNum(Integer traineeNum) {
        this.traineeNum = traineeNum;
    }

    @Override
    public Integer getSubmitNum() {
        return this.submitNum;
    }

    @Override
    public void setSubmitNum(Integer submitNum) {
        this.submitNum = submitNum;
    }

    @Override
    public Integer getNotice() {
        return this.notice;
    }

    @Override
    public void setNotice(Integer notice) {
        this.notice = notice;
    }

    @Override
    public Integer getResourceStatus() {
        return this.resourceStatus;
    }

    @Override
    public void setResourceStatus(Integer resourceStatus) {
        this.resourceStatus = resourceStatus;
    }

    @Override
    public Double getTotalitySatisfied() {
        return this.totalitySatisfied;
    }

    @Override
    public void setTotalitySatisfied(Double totalitySatisfied) {
        this.totalitySatisfied = totalitySatisfied;
    }

    @Override
    public Double getCourseSatisfied() {
        return this.courseSatisfied;
    }

    @Override
    public void setCourseSatisfied(Double courseSatisfied) {
        this.courseSatisfied = courseSatisfied;
    }

    @Override
    public String getOrganizationId() {
        return this.organizationId;
    }

    @Override
    public void setOrganizationId(String organizationId) {
        this.organizationId = organizationId;
    }

    @Override
    public Integer getProjectSource() {
        return this.projectSource;
    }

    @Override
    public void setProjectSource(Integer projectSource) {
        this.projectSource = projectSource;
    }

    @Override
    public Integer getSort() {
        return this.sort;
    }

    @Override
    public void setSort(Integer sort) {
        this.sort = sort;
    }

    @Override
    public Integer getIsOverproof() {
        return this.isOverproof;
    }

    @Override
    public void setIsOverproof(Integer isOverproof) {
        this.isOverproof = isOverproof;
    }

    @Override
    public Integer getFourDegreesSubmitNum() {
        return this.fourDegreesSubmitNum;
    }

    @Override
    public void setFourDegreesSubmitNum(Integer fourDegreesSubmitNum) {
        this.fourDegreesSubmitNum = fourDegreesSubmitNum;
    }

    @Override
    public Integer getAbilitySubmitNum() {
        return this.abilitySubmitNum;
    }

    @Override
    public void setAbilitySubmitNum(Integer abilitySubmitNum) {
        this.abilitySubmitNum = abilitySubmitNum;
    }

    @Override
    public Integer getSuperiorLeadershipSubmitNum() {
        return this.superiorLeadershipSubmitNum;
    }

    @Override
    public void setSuperiorLeadershipSubmitNum(Integer superiorLeadershipSubmitNum) {
        this.superiorLeadershipSubmitNum = superiorLeadershipSubmitNum;
    }

    @Override
    public Integer getQuestionnaireStatus() {
        return this.questionnaireStatus;
    }

    @Override
    public void setQuestionnaireStatus(Integer questionnaireStatus) {
        this.questionnaireStatus = questionnaireStatus;
    }

    @Override
    public Integer getCourseSalary() {
        return this.courseSalary;
    }

    @Override
    public void setCourseSalary(Integer courseSalary) {
        this.courseSalary = courseSalary;
    }

    @Override
    public Integer getSpecialClass() {
        return this.specialClass;
    }

    @Override
    public void setSpecialClass(Integer specialClass) {
        this.specialClass = specialClass;
    }

    @Override
    public Integer getClassLevel() {
        return this.classLevel;
    }

    @Override
    public void setClassLevel(Integer classLevel) {
        this.classLevel = classLevel;
    }

    @Override
    public Integer getView() {
        return this.view;
    }

    @Override
    public void setView(Integer view) {
        this.view = view;
    }

    @Override
    public Timestamp getModifyDate() {
        return this.modifyDate;
    }

    @Override
    public void setModifyDate(Timestamp modifyDate) {
        this.modifyDate = modifyDate;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder("ClassInfoEntity (");

        sb.append(getId());
        sb.append(", ").append(projectId);
        sb.append(", ").append(classTeacherPhone);
        sb.append(", ").append(classTeacher);
        sb.append(", ").append(arriveDate);
        sb.append(", ").append(returnDate);
        sb.append(", ").append(isOutside);
        sb.append(", ").append(surveyType);
        sb.append(", ").append(target);
        sb.append(", ").append(classInfoType);
        sb.append(", ").append(studentType);
        sb.append(", ").append(simpleType);
        sb.append(", ").append(isPlan);
        sb.append(", ").append(status);
        sb.append(", ").append(confirm);
        sb.append(", ").append(groupId);
        sb.append(", ").append(shortName);
        sb.append(", ").append(groupOrder);
        sb.append(", ").append(getCreateTime());
        sb.append(", ").append(createMember);
        sb.append(", ").append(deleteFlag);
        sb.append(", ").append(implementationYear);
        sb.append(", ").append(implementationMonth);
        sb.append(", ").append(memberSatisfaction);
        sb.append(", ").append(classSatisfaction);
        sb.append(", ").append(traineeNum);
        sb.append(", ").append(submitNum);
        sb.append(", ").append(notice);
        sb.append(", ").append(resourceStatus);
        sb.append(", ").append(totalitySatisfied);
        sb.append(", ").append(courseSatisfied);
        sb.append(", ").append(organizationId);
        sb.append(", ").append(projectSource);
        sb.append(", ").append(sort);
        sb.append(", ").append(isOverproof);
        sb.append(", ").append(fourDegreesSubmitNum);
        sb.append(", ").append(abilitySubmitNum);
        sb.append(", ").append(superiorLeadershipSubmitNum);
        sb.append(", ").append(questionnaireStatus);
        sb.append(", ").append(courseSalary);
        sb.append(", ").append(specialClass);
        sb.append(", ").append(classLevel);
        sb.append(", ").append(view);
        sb.append(", ").append(modifyDate);

        sb.append(")");
        return sb.toString();
    }

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public void from(IClassInfo from) {
        setId(from.getId());
        setProjectId(from.getProjectId());
        setClassTeacherPhone(from.getClassTeacherPhone());
        setClassTeacher(from.getClassTeacher());
        setArriveDate(from.getArriveDate());
        setReturnDate(from.getReturnDate());
        setIsOutside(from.getIsOutside());
        setSurveyType(from.getSurveyType());
        setTarget(from.getTarget());
        setClassInfoType(from.getClassInfoType());
        setStudentType(from.getStudentType());
        setSimpleType(from.getSimpleType());
        setIsPlan(from.getIsPlan());
        setStatus(from.getStatus());
        setConfirm(from.getConfirm());
        setGroupId(from.getGroupId());
        setShortName(from.getShortName());
        setGroupOrder(from.getGroupOrder());
        setCreateTime(from.getCreateTime());
        setCreateMember(from.getCreateMember());
        setDeleteFlag(from.getDeleteFlag());
        setImplementationYear(from.getImplementationYear());
        setImplementationMonth(from.getImplementationMonth());
        setMemberSatisfaction(from.getMemberSatisfaction());
        setClassSatisfaction(from.getClassSatisfaction());
        setTraineeNum(from.getTraineeNum());
        setSubmitNum(from.getSubmitNum());
        setNotice(from.getNotice());
        setResourceStatus(from.getResourceStatus());
        setTotalitySatisfied(from.getTotalitySatisfied());
        setCourseSatisfied(from.getCourseSatisfied());
        setOrganizationId(from.getOrganizationId());
        setProjectSource(from.getProjectSource());
        setSort(from.getSort());
        setIsOverproof(from.getIsOverproof());
        setFourDegreesSubmitNum(from.getFourDegreesSubmitNum());
        setAbilitySubmitNum(from.getAbilitySubmitNum());
        setSuperiorLeadershipSubmitNum(from.getSuperiorLeadershipSubmitNum());
        setQuestionnaireStatus(from.getQuestionnaireStatus());
        setCourseSalary(from.getCourseSalary());
        setSpecialClass(from.getSpecialClass());
        setClassLevel(from.getClassLevel());
        setView(from.getView());
        setModifyDate(from.getModifyDate());
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public <E extends IClassInfo> E into(E into) {
        into.from(this);
        return into;
    }

    public void forInsert() {
        this.setId(java.util.UUID.randomUUID().toString());
        this.setCreateTime(System.currentTimeMillis());
    }

    public static final <E extends ClassInfoEntity> org.jooq.RecordMapper<? extends org.jooq.Record, E> createMapper(Class<E> type) {
        return new org.jooq.RecordMapper<org.jooq.Record, E>() {
            @Override
            public E map(org.jooq.Record record) {
                com.zxy.product.train.jooq.tables.records.ClassInfoRecord r = new com.zxy.product.train.jooq.tables.records.ClassInfoRecord();
                org.jooq.Row row = record.fieldsRow();
                row.fieldStream().filter(f -> { return f.toString().equals(com.zxy.product.train.jooq.tables.ClassInfo.CLASS_INFO.ID.toString()); }).findAny().ifPresent(field -> {
                    r.setValue(com.zxy.product.train.jooq.tables.ClassInfo.CLASS_INFO.ID, record.getValue(com.zxy.product.train.jooq.tables.ClassInfo.CLASS_INFO.ID));});
                row.fieldStream().filter(f -> { return f.toString().equals(com.zxy.product.train.jooq.tables.ClassInfo.CLASS_INFO.PROJECT_ID.toString()); }).findAny().ifPresent(field -> {
                    r.setValue(com.zxy.product.train.jooq.tables.ClassInfo.CLASS_INFO.PROJECT_ID, record.getValue(com.zxy.product.train.jooq.tables.ClassInfo.CLASS_INFO.PROJECT_ID));});
                row.fieldStream().filter(f -> { return f.toString().equals(com.zxy.product.train.jooq.tables.ClassInfo.CLASS_INFO.CLASS_TEACHER_PHONE.toString()); }).findAny().ifPresent(field -> {
                    r.setValue(com.zxy.product.train.jooq.tables.ClassInfo.CLASS_INFO.CLASS_TEACHER_PHONE, record.getValue(com.zxy.product.train.jooq.tables.ClassInfo.CLASS_INFO.CLASS_TEACHER_PHONE));});
                row.fieldStream().filter(f -> { return f.toString().equals(com.zxy.product.train.jooq.tables.ClassInfo.CLASS_INFO.CLASS_TEACHER.toString()); }).findAny().ifPresent(field -> {
                    r.setValue(com.zxy.product.train.jooq.tables.ClassInfo.CLASS_INFO.CLASS_TEACHER, record.getValue(com.zxy.product.train.jooq.tables.ClassInfo.CLASS_INFO.CLASS_TEACHER));});
                row.fieldStream().filter(f -> { return f.toString().equals(com.zxy.product.train.jooq.tables.ClassInfo.CLASS_INFO.ARRIVE_DATE.toString()); }).findAny().ifPresent(field -> {
                    r.setValue(com.zxy.product.train.jooq.tables.ClassInfo.CLASS_INFO.ARRIVE_DATE, record.getValue(com.zxy.product.train.jooq.tables.ClassInfo.CLASS_INFO.ARRIVE_DATE));});
                row.fieldStream().filter(f -> { return f.toString().equals(com.zxy.product.train.jooq.tables.ClassInfo.CLASS_INFO.RETURN_DATE.toString()); }).findAny().ifPresent(field -> {
                    r.setValue(com.zxy.product.train.jooq.tables.ClassInfo.CLASS_INFO.RETURN_DATE, record.getValue(com.zxy.product.train.jooq.tables.ClassInfo.CLASS_INFO.RETURN_DATE));});
                row.fieldStream().filter(f -> { return f.toString().equals(com.zxy.product.train.jooq.tables.ClassInfo.CLASS_INFO.IS_OUTSIDE.toString()); }).findAny().ifPresent(field -> {
                    r.setValue(com.zxy.product.train.jooq.tables.ClassInfo.CLASS_INFO.IS_OUTSIDE, record.getValue(com.zxy.product.train.jooq.tables.ClassInfo.CLASS_INFO.IS_OUTSIDE));});
                row.fieldStream().filter(f -> { return f.toString().equals(com.zxy.product.train.jooq.tables.ClassInfo.CLASS_INFO.SURVEY_TYPE.toString()); }).findAny().ifPresent(field -> {
                    r.setValue(com.zxy.product.train.jooq.tables.ClassInfo.CLASS_INFO.SURVEY_TYPE, record.getValue(com.zxy.product.train.jooq.tables.ClassInfo.CLASS_INFO.SURVEY_TYPE));});
                row.fieldStream().filter(f -> { return f.toString().equals(com.zxy.product.train.jooq.tables.ClassInfo.CLASS_INFO.TARGET.toString()); }).findAny().ifPresent(field -> {
                    r.setValue(com.zxy.product.train.jooq.tables.ClassInfo.CLASS_INFO.TARGET, record.getValue(com.zxy.product.train.jooq.tables.ClassInfo.CLASS_INFO.TARGET));});
                row.fieldStream().filter(f -> { return f.toString().equals(com.zxy.product.train.jooq.tables.ClassInfo.CLASS_INFO.CLASS_INFO_TYPE.toString()); }).findAny().ifPresent(field -> {
                    r.setValue(com.zxy.product.train.jooq.tables.ClassInfo.CLASS_INFO.CLASS_INFO_TYPE, record.getValue(com.zxy.product.train.jooq.tables.ClassInfo.CLASS_INFO.CLASS_INFO_TYPE));});
                row.fieldStream().filter(f -> { return f.toString().equals(com.zxy.product.train.jooq.tables.ClassInfo.CLASS_INFO.STUDENT_TYPE.toString()); }).findAny().ifPresent(field -> {
                    r.setValue(com.zxy.product.train.jooq.tables.ClassInfo.CLASS_INFO.STUDENT_TYPE, record.getValue(com.zxy.product.train.jooq.tables.ClassInfo.CLASS_INFO.STUDENT_TYPE));});
                row.fieldStream().filter(f -> { return f.toString().equals(com.zxy.product.train.jooq.tables.ClassInfo.CLASS_INFO.SIMPLE_TYPE.toString()); }).findAny().ifPresent(field -> {
                    r.setValue(com.zxy.product.train.jooq.tables.ClassInfo.CLASS_INFO.SIMPLE_TYPE, record.getValue(com.zxy.product.train.jooq.tables.ClassInfo.CLASS_INFO.SIMPLE_TYPE));});
                row.fieldStream().filter(f -> { return f.toString().equals(com.zxy.product.train.jooq.tables.ClassInfo.CLASS_INFO.IS_PLAN.toString()); }).findAny().ifPresent(field -> {
                    r.setValue(com.zxy.product.train.jooq.tables.ClassInfo.CLASS_INFO.IS_PLAN, record.getValue(com.zxy.product.train.jooq.tables.ClassInfo.CLASS_INFO.IS_PLAN));});
                row.fieldStream().filter(f -> { return f.toString().equals(com.zxy.product.train.jooq.tables.ClassInfo.CLASS_INFO.STATUS.toString()); }).findAny().ifPresent(field -> {
                    r.setValue(com.zxy.product.train.jooq.tables.ClassInfo.CLASS_INFO.STATUS, record.getValue(com.zxy.product.train.jooq.tables.ClassInfo.CLASS_INFO.STATUS));});
                row.fieldStream().filter(f -> { return f.toString().equals(com.zxy.product.train.jooq.tables.ClassInfo.CLASS_INFO.CONFIRM.toString()); }).findAny().ifPresent(field -> {
                    r.setValue(com.zxy.product.train.jooq.tables.ClassInfo.CLASS_INFO.CONFIRM, record.getValue(com.zxy.product.train.jooq.tables.ClassInfo.CLASS_INFO.CONFIRM));});
                row.fieldStream().filter(f -> { return f.toString().equals(com.zxy.product.train.jooq.tables.ClassInfo.CLASS_INFO.GROUP_ID.toString()); }).findAny().ifPresent(field -> {
                    r.setValue(com.zxy.product.train.jooq.tables.ClassInfo.CLASS_INFO.GROUP_ID, record.getValue(com.zxy.product.train.jooq.tables.ClassInfo.CLASS_INFO.GROUP_ID));});
                row.fieldStream().filter(f -> { return f.toString().equals(com.zxy.product.train.jooq.tables.ClassInfo.CLASS_INFO.SHORT_NAME.toString()); }).findAny().ifPresent(field -> {
                    r.setValue(com.zxy.product.train.jooq.tables.ClassInfo.CLASS_INFO.SHORT_NAME, record.getValue(com.zxy.product.train.jooq.tables.ClassInfo.CLASS_INFO.SHORT_NAME));});
                row.fieldStream().filter(f -> { return f.toString().equals(com.zxy.product.train.jooq.tables.ClassInfo.CLASS_INFO.GROUP_ORDER.toString()); }).findAny().ifPresent(field -> {
                    r.setValue(com.zxy.product.train.jooq.tables.ClassInfo.CLASS_INFO.GROUP_ORDER, record.getValue(com.zxy.product.train.jooq.tables.ClassInfo.CLASS_INFO.GROUP_ORDER));});
                row.fieldStream().filter(f -> { return f.toString().equals(com.zxy.product.train.jooq.tables.ClassInfo.CLASS_INFO.CREATE_TIME.toString()); }).findAny().ifPresent(field -> {
                    r.setValue(com.zxy.product.train.jooq.tables.ClassInfo.CLASS_INFO.CREATE_TIME, record.getValue(com.zxy.product.train.jooq.tables.ClassInfo.CLASS_INFO.CREATE_TIME));});
                row.fieldStream().filter(f -> { return f.toString().equals(com.zxy.product.train.jooq.tables.ClassInfo.CLASS_INFO.CREATE_MEMBER.toString()); }).findAny().ifPresent(field -> {
                    r.setValue(com.zxy.product.train.jooq.tables.ClassInfo.CLASS_INFO.CREATE_MEMBER, record.getValue(com.zxy.product.train.jooq.tables.ClassInfo.CLASS_INFO.CREATE_MEMBER));});
                row.fieldStream().filter(f -> { return f.toString().equals(com.zxy.product.train.jooq.tables.ClassInfo.CLASS_INFO.DELETE_FLAG.toString()); }).findAny().ifPresent(field -> {
                    r.setValue(com.zxy.product.train.jooq.tables.ClassInfo.CLASS_INFO.DELETE_FLAG, record.getValue(com.zxy.product.train.jooq.tables.ClassInfo.CLASS_INFO.DELETE_FLAG));});
                row.fieldStream().filter(f -> { return f.toString().equals(com.zxy.product.train.jooq.tables.ClassInfo.CLASS_INFO.IMPLEMENTATION_YEAR.toString()); }).findAny().ifPresent(field -> {
                    r.setValue(com.zxy.product.train.jooq.tables.ClassInfo.CLASS_INFO.IMPLEMENTATION_YEAR, record.getValue(com.zxy.product.train.jooq.tables.ClassInfo.CLASS_INFO.IMPLEMENTATION_YEAR));});
                row.fieldStream().filter(f -> { return f.toString().equals(com.zxy.product.train.jooq.tables.ClassInfo.CLASS_INFO.IMPLEMENTATION_MONTH.toString()); }).findAny().ifPresent(field -> {
                    r.setValue(com.zxy.product.train.jooq.tables.ClassInfo.CLASS_INFO.IMPLEMENTATION_MONTH, record.getValue(com.zxy.product.train.jooq.tables.ClassInfo.CLASS_INFO.IMPLEMENTATION_MONTH));});
                row.fieldStream().filter(f -> { return f.toString().equals(com.zxy.product.train.jooq.tables.ClassInfo.CLASS_INFO.MEMBER_SATISFACTION.toString()); }).findAny().ifPresent(field -> {
                    r.setValue(com.zxy.product.train.jooq.tables.ClassInfo.CLASS_INFO.MEMBER_SATISFACTION, record.getValue(com.zxy.product.train.jooq.tables.ClassInfo.CLASS_INFO.MEMBER_SATISFACTION));});
                row.fieldStream().filter(f -> { return f.toString().equals(com.zxy.product.train.jooq.tables.ClassInfo.CLASS_INFO.CLASS_SATISFACTION.toString()); }).findAny().ifPresent(field -> {
                    r.setValue(com.zxy.product.train.jooq.tables.ClassInfo.CLASS_INFO.CLASS_SATISFACTION, record.getValue(com.zxy.product.train.jooq.tables.ClassInfo.CLASS_INFO.CLASS_SATISFACTION));});
                row.fieldStream().filter(f -> { return f.toString().equals(com.zxy.product.train.jooq.tables.ClassInfo.CLASS_INFO.TRAINEE_NUM.toString()); }).findAny().ifPresent(field -> {
                    r.setValue(com.zxy.product.train.jooq.tables.ClassInfo.CLASS_INFO.TRAINEE_NUM, record.getValue(com.zxy.product.train.jooq.tables.ClassInfo.CLASS_INFO.TRAINEE_NUM));});
                row.fieldStream().filter(f -> { return f.toString().equals(com.zxy.product.train.jooq.tables.ClassInfo.CLASS_INFO.SUBMIT_NUM.toString()); }).findAny().ifPresent(field -> {
                    r.setValue(com.zxy.product.train.jooq.tables.ClassInfo.CLASS_INFO.SUBMIT_NUM, record.getValue(com.zxy.product.train.jooq.tables.ClassInfo.CLASS_INFO.SUBMIT_NUM));});
                row.fieldStream().filter(f -> { return f.toString().equals(com.zxy.product.train.jooq.tables.ClassInfo.CLASS_INFO.NOTICE.toString()); }).findAny().ifPresent(field -> {
                    r.setValue(com.zxy.product.train.jooq.tables.ClassInfo.CLASS_INFO.NOTICE, record.getValue(com.zxy.product.train.jooq.tables.ClassInfo.CLASS_INFO.NOTICE));});
                row.fieldStream().filter(f -> { return f.toString().equals(com.zxy.product.train.jooq.tables.ClassInfo.CLASS_INFO.RESOURCE_STATUS.toString()); }).findAny().ifPresent(field -> {
                    r.setValue(com.zxy.product.train.jooq.tables.ClassInfo.CLASS_INFO.RESOURCE_STATUS, record.getValue(com.zxy.product.train.jooq.tables.ClassInfo.CLASS_INFO.RESOURCE_STATUS));});
                row.fieldStream().filter(f -> { return f.toString().equals(com.zxy.product.train.jooq.tables.ClassInfo.CLASS_INFO.TOTALITY_SATISFIED.toString()); }).findAny().ifPresent(field -> {
                    r.setValue(com.zxy.product.train.jooq.tables.ClassInfo.CLASS_INFO.TOTALITY_SATISFIED, record.getValue(com.zxy.product.train.jooq.tables.ClassInfo.CLASS_INFO.TOTALITY_SATISFIED));});
                row.fieldStream().filter(f -> { return f.toString().equals(com.zxy.product.train.jooq.tables.ClassInfo.CLASS_INFO.COURSE_SATISFIED.toString()); }).findAny().ifPresent(field -> {
                    r.setValue(com.zxy.product.train.jooq.tables.ClassInfo.CLASS_INFO.COURSE_SATISFIED, record.getValue(com.zxy.product.train.jooq.tables.ClassInfo.CLASS_INFO.COURSE_SATISFIED));});
                row.fieldStream().filter(f -> { return f.toString().equals(com.zxy.product.train.jooq.tables.ClassInfo.CLASS_INFO.ORGANIZATION_ID.toString()); }).findAny().ifPresent(field -> {
                    r.setValue(com.zxy.product.train.jooq.tables.ClassInfo.CLASS_INFO.ORGANIZATION_ID, record.getValue(com.zxy.product.train.jooq.tables.ClassInfo.CLASS_INFO.ORGANIZATION_ID));});
                row.fieldStream().filter(f -> { return f.toString().equals(com.zxy.product.train.jooq.tables.ClassInfo.CLASS_INFO.PROJECT_SOURCE.toString()); }).findAny().ifPresent(field -> {
                    r.setValue(com.zxy.product.train.jooq.tables.ClassInfo.CLASS_INFO.PROJECT_SOURCE, record.getValue(com.zxy.product.train.jooq.tables.ClassInfo.CLASS_INFO.PROJECT_SOURCE));});
                row.fieldStream().filter(f -> { return f.toString().equals(com.zxy.product.train.jooq.tables.ClassInfo.CLASS_INFO.SORT.toString()); }).findAny().ifPresent(field -> {
                    r.setValue(com.zxy.product.train.jooq.tables.ClassInfo.CLASS_INFO.SORT, record.getValue(com.zxy.product.train.jooq.tables.ClassInfo.CLASS_INFO.SORT));});
                row.fieldStream().filter(f -> { return f.toString().equals(com.zxy.product.train.jooq.tables.ClassInfo.CLASS_INFO.IS_OVERPROOF.toString()); }).findAny().ifPresent(field -> {
                    r.setValue(com.zxy.product.train.jooq.tables.ClassInfo.CLASS_INFO.IS_OVERPROOF, record.getValue(com.zxy.product.train.jooq.tables.ClassInfo.CLASS_INFO.IS_OVERPROOF));});
                row.fieldStream().filter(f -> { return f.toString().equals(com.zxy.product.train.jooq.tables.ClassInfo.CLASS_INFO.FOUR_DEGREES_SUBMIT_NUM.toString()); }).findAny().ifPresent(field -> {
                    r.setValue(com.zxy.product.train.jooq.tables.ClassInfo.CLASS_INFO.FOUR_DEGREES_SUBMIT_NUM, record.getValue(com.zxy.product.train.jooq.tables.ClassInfo.CLASS_INFO.FOUR_DEGREES_SUBMIT_NUM));});
                row.fieldStream().filter(f -> { return f.toString().equals(com.zxy.product.train.jooq.tables.ClassInfo.CLASS_INFO.ABILITY_SUBMIT_NUM.toString()); }).findAny().ifPresent(field -> {
                    r.setValue(com.zxy.product.train.jooq.tables.ClassInfo.CLASS_INFO.ABILITY_SUBMIT_NUM, record.getValue(com.zxy.product.train.jooq.tables.ClassInfo.CLASS_INFO.ABILITY_SUBMIT_NUM));});
                row.fieldStream().filter(f -> { return f.toString().equals(com.zxy.product.train.jooq.tables.ClassInfo.CLASS_INFO.SUPERIOR_LEADERSHIP_SUBMIT_NUM.toString()); }).findAny().ifPresent(field -> {
                    r.setValue(com.zxy.product.train.jooq.tables.ClassInfo.CLASS_INFO.SUPERIOR_LEADERSHIP_SUBMIT_NUM, record.getValue(com.zxy.product.train.jooq.tables.ClassInfo.CLASS_INFO.SUPERIOR_LEADERSHIP_SUBMIT_NUM));});
                row.fieldStream().filter(f -> { return f.toString().equals(com.zxy.product.train.jooq.tables.ClassInfo.CLASS_INFO.QUESTIONNAIRE_STATUS.toString()); }).findAny().ifPresent(field -> {
                    r.setValue(com.zxy.product.train.jooq.tables.ClassInfo.CLASS_INFO.QUESTIONNAIRE_STATUS, record.getValue(com.zxy.product.train.jooq.tables.ClassInfo.CLASS_INFO.QUESTIONNAIRE_STATUS));});
                row.fieldStream().filter(f -> { return f.toString().equals(com.zxy.product.train.jooq.tables.ClassInfo.CLASS_INFO.COURSE_SALARY.toString()); }).findAny().ifPresent(field -> {
                    r.setValue(com.zxy.product.train.jooq.tables.ClassInfo.CLASS_INFO.COURSE_SALARY, record.getValue(com.zxy.product.train.jooq.tables.ClassInfo.CLASS_INFO.COURSE_SALARY));});
                row.fieldStream().filter(f -> { return f.toString().equals(com.zxy.product.train.jooq.tables.ClassInfo.CLASS_INFO.SPECIAL_CLASS.toString()); }).findAny().ifPresent(field -> {
                    r.setValue(com.zxy.product.train.jooq.tables.ClassInfo.CLASS_INFO.SPECIAL_CLASS, record.getValue(com.zxy.product.train.jooq.tables.ClassInfo.CLASS_INFO.SPECIAL_CLASS));});
                row.fieldStream().filter(f -> { return f.toString().equals(com.zxy.product.train.jooq.tables.ClassInfo.CLASS_INFO.CLASS_LEVEL.toString()); }).findAny().ifPresent(field -> {
                    r.setValue(com.zxy.product.train.jooq.tables.ClassInfo.CLASS_INFO.CLASS_LEVEL, record.getValue(com.zxy.product.train.jooq.tables.ClassInfo.CLASS_INFO.CLASS_LEVEL));});
                row.fieldStream().filter(f -> { return f.toString().equals(com.zxy.product.train.jooq.tables.ClassInfo.CLASS_INFO.VIEW.toString()); }).findAny().ifPresent(field -> {
                    r.setValue(com.zxy.product.train.jooq.tables.ClassInfo.CLASS_INFO.VIEW, record.getValue(com.zxy.product.train.jooq.tables.ClassInfo.CLASS_INFO.VIEW));});
                row.fieldStream().filter(f -> { return f.toString().equals(com.zxy.product.train.jooq.tables.ClassInfo.CLASS_INFO.MODIFY_DATE.toString()); }).findAny().ifPresent(field -> {
                    r.setValue(com.zxy.product.train.jooq.tables.ClassInfo.CLASS_INFO.MODIFY_DATE, record.getValue(com.zxy.product.train.jooq.tables.ClassInfo.CLASS_INFO.MODIFY_DATE));});
                try {
                    E pojo = type.newInstance();
                    pojo.from(r);
                    return pojo;
                } catch (Exception e) {
                    e.printStackTrace(); // ignored
                }
                return null;
            }
        };
    }}
