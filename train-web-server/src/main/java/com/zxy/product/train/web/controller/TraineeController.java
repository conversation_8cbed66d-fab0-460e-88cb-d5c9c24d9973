package com.zxy.product.train.web.controller;

import com.google.common.collect.ImmutableMap;
import com.zxy.common.base.exception.UnprocessableException;
import com.zxy.common.base.helper.PagedResult;
import com.zxy.common.cache.Cache;
import com.zxy.common.cache.CacheService;
import com.zxy.common.message.provider.MessageSender;
import com.zxy.common.office.excel.Reader;
import com.zxy.common.office.excel.ValidateContext;
import com.zxy.common.office.excel.export.Writer;
import com.zxy.common.office.excel.export.support.ExcelWriter;
import com.zxy.common.office.excel.support.DefaultReader;
import com.zxy.common.office.excel.support.validator.RequiredValidator;
import com.zxy.common.office.excel.support.validator.TrueValidator;
import com.zxy.common.restful.RequestContext;
import com.zxy.common.restful.annotation.Param;
import com.zxy.common.restful.audit.Audit;
import com.zxy.common.restful.json.JSON;
import com.zxy.common.restful.security.Permitted;
import com.zxy.common.restful.security.Subject;
import com.zxy.common.restful.util.Encrypt;
import com.zxy.product.human.api.MemberConfigService;
import com.zxy.product.human.api.MemberService;
import com.zxy.product.human.entity.MemberConfig;
import com.zxy.product.system.api.operation.MessageSendService;
import com.zxy.product.system.api.setting.RuleConfigService;
import com.zxy.product.system.content.MessageConstant;
import com.zxy.product.system.entity.RuleConfig;
import com.zxy.product.train.api.*;
import com.zxy.product.train.content.ErrorCode;
import com.zxy.product.train.content.MessageHeaderContent;
import com.zxy.product.train.content.MessageTypeContent;
import com.zxy.product.train.entity.*;
import com.zxy.product.train.util.DesensitizationUtil;
import com.zxy.product.train.web.controller.xlsx.BaseImportController;
import com.zxy.product.train.web.util.BrowserUtil;
import com.zxy.product.train.web.util.DateUtil;
import com.zxy.product.train.web.util.ImportExportUtil;
import org.apache.poi.hssf.usermodel.*;
import org.apache.poi.hssf.util.HSSFColor;
import org.apache.poi.ss.usermodel.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.EnvironmentAware;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Controller;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.OutputStream;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.function.Function;
import java.util.function.Supplier;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static com.zxy.common.restful.util.Encrypt.Decrypt;

/**
 * <AUTHOR>
 * @date 17/02/12
 */
@Controller
@RequestMapping("/trainee")
public class TraineeController extends BaseImportController  implements EnvironmentAware {

    public static final String TEMPLATE_NAME = "更新排序模板.xlsx";

    private TraineeService traineeService;
    private ClassstaffClassService classstaffClassService;
    private MemberService memberService;
    private MemberConfigService memberConfigService;
    private MessageRecordService messageRecordService;
    private MessageSendService messageSendService;

    private ClassInfoService classInfoService;

    private ClassEvaluateService classEvaluateService;

    private SignDetailService signDetailService;
    private MessageSender messageSender;
    private ClassSignupInfoService signupService;
    private Cache studyCache;
    private Cache memberCache;

    private GroupConfigurationValueService groupConfigurationValueService;
    private SettlementService settlementService;
    private RuleConfigService ruleConfigService;

    private String aesKey;

    @Autowired
    public void setSignupService(ClassSignupInfoService signupService) {
        this.signupService = signupService;
    }

    @Autowired
    public void setSettlementService(SettlementService settlementService) {
        this.settlementService = settlementService;
    }
	@Autowired
	public void setGroupConfigurationValueService(GroupConfigurationValueService groupConfigurationValueService) {
		this.groupConfigurationValueService = groupConfigurationValueService;
	}
    @Autowired
    public void setMemberConfigService(MemberConfigService memberConfigService) {
        this.memberConfigService = memberConfigService;
    }
    @Autowired
    public void setClassInfoService(ClassInfoService classInfoService) {
        this.classInfoService = classInfoService;
    }

    @Autowired
    public void setMessageRecordService(MessageRecordService messageRecordService) {
        this.messageRecordService = messageRecordService;
    }

    @Autowired
    public void setTraineeService(TraineeService traineeService) {
        this.traineeService = traineeService;
    }

    @Autowired
    public void setMemberService(MemberService memberService) {
        this.memberService = memberService;
    }

    @Autowired
    public void setClassstaffClassService(ClassstaffClassService classstaffClassService) {
        this.classstaffClassService = classstaffClassService;
    }


    @Autowired
    public void setMessageSendService(MessageSendService messageSendService) {
        this.messageSendService = messageSendService;
    }

    @Autowired
    public void setClassEvaluateService(ClassEvaluateService classEvaluateService) {
        this.classEvaluateService = classEvaluateService;
    }


    @Autowired
    public void setSignDetailService(SignDetailService signDetailService) {
        this.signDetailService = signDetailService;
    }

    @Autowired
    public void setMessageSender(MessageSender messageSender) {
        this.messageSender = messageSender;
    }

    @Autowired
    public void setCacheService(CacheService cacheService) {
        this.memberCache = cacheService.create(Trainee.CACHE_TRAINEE_MEMBER_COURSE_KEY);
        this.studyCache = cacheService.create(Trainee.CACHE_TRAINEE_STUDY_TIME_KEY);
    }

    @Autowired
    public void setRuleConfigService(RuleConfigService ruleConfigService) {
        this.ruleConfigService = ruleConfigService;
    }

    /**
     * 成员管理正式学员列表
     *
     * @param requestContext
     * @return
     */
    @RequestMapping(value = "/trainees", method = RequestMethod.GET)
    @Param(name = "page", type = Integer.class, required = true)
    @Param(name = "pageSize", type = Integer.class, required = true)
    @Param(name = "classId", type = String.class, required = true)
    @Param(name = "auditStatus", type = Integer.class)
    @Param(name = "memberName", type = String.class)
    @Param(name = "memberFullName", type = String.class)
    @Param(name = "organizationName", type = String.class)
    @Param(name = "organizationId", type = String.class)
    @Param(name = "groupId", type = String.class)
    @Param(name = "commitQuestionary", type = Integer.class)
    @Param(name = "source", type = Integer.class)
    @Param(name = "register", type = Integer.class)
    @Param(name = "sortType", type = Integer.class)
    @JSON("recordCount")
    @JSON("items.(id,organizationId,organizationName,organizationLevel,companyName,"
    		+ "phoneNumber,email,auditStatus,sort,sortForGroup,sex,levelId,levelName,"
    		+ "nation,remark,nationName,organizationOrder,commitQuestionary,"
    		+ "memberId,traineeGroupId,newCompany,newOrganization,register)")
    @JSON("items.member.(name,fullName,jobName,headPortrait,headPortraitPath)")
    @JSON("items.traineeGroup.(id,name)")
    public PagedResult<Trainee> findformalTrainees(RequestContext requestContext, Subject<Member> subject) {
        PagedResult<Trainee> traineePagedResult = traineeService.find(requestContext.get("page", Integer.class),
                                                                      requestContext.get("pageSize", Integer.class), requestContext.get("classId", String.class),
                                                                      Optional.of(Trainee.TRAINEE_TYPE_FORMAL), requestContext.getOptional("memberName", String.class),
                                                                      requestContext.getOptional("memberFullName", String.class),
                                                                      requestContext.getOptional("organizationName", String.class),
                                                                      requestContext.getOptional("groupId", String.class),
                                                                      requestContext.getOptional("organizationId", String.class),
                                                                      requestContext.getOptional("auditStatus", Integer.class),
                                                                      requestContext.getOptional("commitQuestionary", Integer.class),
                                                                      requestContext.getOptional("source", Integer.class),
                                                                      requestContext.getOptional("register", Integer.class),
                                                                      requestContext.getOptional("sortType", Integer.class));

        List<Trainee> items = traineePagedResult.getItems();
        Optional.ofNullable(items)
                .ifPresent(list -> list.forEach(x -> {
                    String phoneNumber = x.getPhoneNumber();
                    if (phoneNumber != null) {
                        x.setPhoneNumber(getAesEncryptParam(Optional.of(phoneNumber)));
                    }
                }));
        return traineePagedResult;
    }

    /**
     * 成员管理正式学员列表
     *
     * @param requestContext
     * @return
     */
    @RequestMapping(value = "/group-member", method = RequestMethod.GET)
    @Param(name = "page", type = Integer.class, required = true)
    @Param(name = "pageSize", type = Integer.class, required = true)
    @Param(name = "classId", required = true)
    @Param(name = "auditStatus", type = Integer.class)
    @Param(name = "groupId")
    @JSON("recordCount")
    @JSON("items.(id,organizationId,organizationName,organizationLevel,companyName,"
            + "phoneNumber,email,auditStatus,sort,sortForGroup,sex,levelId,levelName,"
            + "nation,remark,nationName,organizationOrder,commitQuestionary,"
            + "memberId,traineeGroupId,newCompany,newOrganization,register)")
    @JSON("items.member.(name,fullName,jobName,headPortrait,headPortraitPath)")
    public PagedResult<Trainee> findGroupMember(RequestContext requestContext, Subject<Member> subject) {
        PagedResult<Trainee> traineePagedResult = traineeService.findGroupMember(requestContext.get("page", Integer.class),
                                                                                 requestContext.get("pageSize", Integer.class),
                                                                                 requestContext.get("classId", String.class),
                                                                                 Optional.of(Trainee.TRAINEE_TYPE_FORMAL),
                                                                                 requestContext.getOptional("groupId", String.class),
                                                                                 requestContext.getOptional("auditStatus", Integer.class));

        List<Trainee> items = traineePagedResult.getItems();
        Optional.ofNullable(items)
                .ifPresent(list -> list.forEach(x -> {
                    String phoneNumber = x.getPhoneNumber();
                    if (phoneNumber != null) {
                        x.setPhoneNumber(getAesEncryptParam(Optional.of(phoneNumber)));
                    }
                }));
        return traineePagedResult;
    }

    /**
     * 成员管理非正式学员列表
     *
     * @param requestContext
     * @return
     */
    @RequestMapping(value = "/informal-trainees", method = RequestMethod.GET)
    @Param(name = "page", type = Integer.class, required = true)
    @Param(name = "pageSize", type = Integer.class, required = true)
    @Param(name = "classId", type = String.class, required = true)
    @JSON("recordCount")
    @JSON("items.(id,organizationName,organizationLevel,companyName,newCompany,phoneNumber)")
    @JSON("items.member.(name,fullName,jobName)")
    public PagedResult<Trainee> findInformalTrainees(RequestContext requestContext) {
        PagedResult<Trainee> traineePagedResult = traineeService.find(requestContext.get("page", Integer.class),
                                                                      requestContext.get("pageSize", Integer.class), requestContext.get("classId", String.class),
                                                                      Optional.of(Trainee.TRAINEE_TYPE_INFORMAL), Optional.empty(), Optional.empty(), Optional.empty(),
                                                                      Optional.empty(), Optional.empty(), Optional.empty(), Optional.empty(), Optional.empty(), Optional.empty(), Optional.empty());
        List<Trainee> items = traineePagedResult.getItems();
        Optional.ofNullable(items)
                .ifPresent(list -> list.forEach(x -> {
                    String phoneNumber = x.getPhoneNumber();
                    if (phoneNumber != null) {
                        x.setPhoneNumber(getAesEncryptParam(Optional.of(phoneNumber)));
                    }
                }));
        return traineePagedResult;
    }

    /**
     * 响应中心正式学员列表
     *
     * @param requestContext
     * @return
     */
    @RequestMapping(value = "/response-trainees", method = RequestMethod.GET)
    @Param(name = "page", type = Integer.class, required = true)
    @Param(name = "pageSize", type = Integer.class, required = true)
    @Param(name = "classId", type = String.class, required = true)
    @Param(name = "memberFullName", type = String.class)
    @Param(name = "organizationId", type = String.class)
    @Param(name = "auditStatus", type = Integer.class)
    @JSON("recordCount")
    @JSON("items.(id,organizationName,organizationLevel,companyName,phoneNumber,newCompany,auditStatus,email,commitQuestionary)")
    @JSON("items.member.(name,fullName,jobName)")
    public PagedResult<Trainee> responseFind(RequestContext requestContext) {
        return traineeService.findForResponseCenter(requestContext.get("page", Integer.class),
                requestContext.get("pageSize", Integer.class), requestContext.get("classId", String.class),
                requestContext.getOptional("memberFullName", String.class),
                requestContext.getOptional("organizationId", String.class),
                requestContext.getOptional("auditStatus", Integer.class));
    }

    /**
     * 待分组学员
     *
     * @param requestContext
     * @param subject
     * @return
     */
    @RequestMapping(value = "/wait-group-trainees", method = RequestMethod.GET)
    @Param(name = "page", type = Integer.class, required = true)
    @Param(name = "pageSize", type = Integer.class, required = true)
    @Param(name = "classId", type = String.class, required = true)
    @Param(name = "groupId", type = String.class, required = true)
    @Param(name = "memberFullName", type = String.class)
    @Param(name = "memberName", type = String.class)
    @Param(name = "organizationName", type = String.class)
    @JSON("recordCount")
    @JSON("items.(id,organizationId,organizationName,organizationLevel,companyName,phoneNumber,auditStatus,sort,sortForGroup,sex,nationName)")
    @JSON("items.member.(name,fullName,jobName)")
    public PagedResult<Trainee> findWaitGroupTrainees(RequestContext requestContext, Subject<Member> subject) {
        return traineeService.findWaitGroupTrainees(requestContext.get("page", Integer.class),
                requestContext.get("pageSize", Integer.class), requestContext.get("classId", String.class),
                requestContext.get("groupId", String.class), requestContext.getOptionalString("memberFullName"),
                requestContext.getOptionalString("memberName"), requestContext.getOptionalString("organizationName"));
    }

    /**
     * 按分组查询学员
     *
     * @param requestContext
     * @param subject
     * @return
     */
    @RequestMapping(value = "/group-trainees", method = RequestMethod.GET)
    @Param(name = "classId", type = String.class, required = true)
    @Param(name = "groupId", type = String.class, required = true)
    @JSON("id,sort,organizationOrder,organizationName,sortForGroup")
    @JSON("member.(fullName,name,sex)")
    public List<Trainee> findByGroup(RequestContext requestContext, Subject<Member> subject) {
        return traineeService.findByGroup(requestContext.get("classId", String.class),
                requestContext.get("groupId", String.class));
    }

    /**
     * 通过编号memberName添加学员
     *
     * @param requestContext
     * @return
     */
    @RequestMapping(value = "/add-trainee", method = RequestMethod.POST)
    @Param(name = "classId", type = String.class, required = true)
    @Param(name = "type", type = Integer.class, required = true)
    @Param(name = "memberName", type = String.class, required = true)
    @Param(name = "memberId", type = String.class)
    @JSON("*")
    @Audit(module = "活动管理", subModule = "培训管理－班级管理", action = Audit.Action.INSERT, fisrtAction = "班级维护", secondAction ="成员管理—添加学员", desc = "操作添加学员{0}于班级{1}", params = {"memberName"},ids = {"classId"}, jsons = {"name"}, keys = {"class-info"},businessType="type", businessValue="0")
    @Audit(module = "活动管理", subModule = "培训管理－班级管理", action = Audit.Action.INSERT, fisrtAction = "班级维护", secondAction ="成员管理—添加非正式成员", desc = "操作添加非正式成员{0}于班级{1}", params = {"memberName"},ids = {"classId"}, jsons = {"name"}, keys = {"class-info"},businessType="type", businessValue="1")
    public int addTrainee(RequestContext requestContext, Subject<Member> subject) {
        Integer type = requestContext.getInteger("type");
        String classId = requestContext.get("classId", String.class);
        Optional<String> memberId = requestContext.getOptional("memberId", String.class);
        boolean flag = settlementService.findSettlementCount(classId)==0?false:true;
        // 根据公司id和员工编号查询member
        Optional<com.zxy.product.human.entity.Member> OpMember = Optional.empty();
            OpMember = memberService
                    .getMemberByName(requestContext.getString("memberName"));
//        }
            // 如果系统存在该员工做添加操作
            int addNum = 0;
            if (OpMember.isPresent()) {
                if (OpMember.get().getStatus() == 1) {
                    String[] memberIds = new String[1];
                    memberIds[0] = OpMember.get().getId();
                    if(flag){
                        Optional<Trainee> trainee =  traineeService.getClassFormalTrainee(requestContext.getString("classId"),OpMember.get().getId());
                        if (trainee.isPresent() && trainee.get()!=null&& trainee.get().getFinance().equals(2)){
                            trainee.get().setDeleteFlag(0);
                            trainee.get().setFinance(null);
                            traineeService.updateTrainee(trainee.get());
                            addNum = 1;
                    }else {
                            addNum = traineeService.addTrainee(requestContext.getString("classId"), type,
                                    OpMember.get().getId(), subject.getCurrentUserId(),Optional.of(1),Optional.empty());
                        }
                    }else {
                    addNum = traineeService.addTrainee(requestContext.getString("classId"), type,
                            OpMember.get().getId(), subject.getCurrentUserId(),Optional.empty(),Optional.empty());
                     }
                    memberCache.clear(Trainee.CACHE_TRAINEE_MEMBER_COURSE_KEY + requestContext.getString("classId"));
                    studyCache.clear(Trainee.CACHE_TRAINEE_STUDY_TIME_KEY + requestContext.getString("classId"));
                    if (addNum == 1 && type.equals(0)) {
                        signDetailService.insertDetail(classId, memberIds);
                        Integer num = traineeService.countTrainee(classId);
                        classInfoService.updateTraineeNum(classId, num,false,false,false);
                    }
                    return addNum;
                } else {
                    return 1111;
                }
            }if(memberId.isPresent()){
            Member member = traineeService.getMember(memberId.get());
            if (member.getStatus()!=null && 1 == member.getStatus()  ) {
                String[] memberIds = new String[1];
                memberIds[0] = memberId.get();
                if(flag){
                    addNum = traineeService.addTrainee(requestContext.getString("classId"), type,
                            memberId.get(), subject.getCurrentUserId(),Optional.of(1),Optional.empty());
                }else {
                    addNum = traineeService.addTrainee(requestContext.getString("classId"), type,
                            memberId.get(), subject.getCurrentUserId(),Optional.empty(),Optional.empty());
                }
                memberCache.clear(Trainee.CACHE_TRAINEE_MEMBER_COURSE_KEY + requestContext.getString("classId"));
                studyCache.clear(Trainee.CACHE_TRAINEE_STUDY_TIME_KEY + requestContext.getString("classId"));
                if (addNum == 1 && OpMember.isPresent() && type.equals(0)) {
                    signDetailService.insertDetail(classId, memberIds);
                    Integer num = traineeService.countTrainee(classId);
                    classInfoService.updateTraineeNum(classId, num,false,false,false);
                }
                return addNum;
            } else {
                return 1111;
            }
        }
        return 666;
    }
    @RequestMapping(value = "/select-organization", method = RequestMethod.POST)
    @Param(name = "memberName", type = String.class, required = true)
    @JSON("id,name,memberId")
    public List<Organization> selectOrganization(RequestContext requestContext){
//        com.zxy.product.human.entity.Organization humanOrganization = memberService
//                .getCompanyOrganizationWithLevel2ByMemberId(subject.getCurrentUserId());
        List<com.zxy.product.human.entity.Member> list = memberService
                .listMemberWithSameIdCard("1,", requestContext.getString("memberName"));
        //如果查询不到账号，返回异常
        if(list==null|| list.isEmpty()){
            throw new UnprocessableException(ErrorCode.NoBody);
        }
        //过滤掉禁用的账号
        List<com.zxy.product.human.entity.Member> enableList=list.stream().filter(
                member ->com.zxy.product.human.entity.Member.STATUS_ENABLED.equals(member.getStatus()))
                .collect(Collectors.toList());
        //如果查询不到可用账号，返回异常
        if(enableList==null||enableList.isEmpty()){
            throw new UnprocessableException(ErrorCode.NoBody);
        }
        List<Organization> orgList = new ArrayList();
        for (int i = 0;i< enableList.size(); i++){
            Organization organization = new Organization();
            organization.setMemberId(enableList.get(i).getId());
            organization.setId(enableList.get(i).getOrganization().getId());
            organization.setName(enableList.get(i).getOrganization().getName());
            orgList.add(organization);
        }
        if(orgList.size()<=0){
            throw new UnprocessableException(ErrorCode.NoBody);
        }
        return orgList;
    }

    /**
     * 通过memberIds添加多个学员
     *
     * @param requestContext
     * @return
     */
    @RequestMapping(value = "/add-trainees", method = RequestMethod.POST)
    @Param(name = "classId", type = String.class, required = true)
    @Param(name = "type", type = Integer.class, required = true)
    @Param(name = "memberIds", type = String.class, required = true)
    @JSON("*")
    public int[] addTrainees(RequestContext requestContext, Subject<Member> subject) {
        String classId = requestContext.get("classId", String.class);
        String memberIdsStr = requestContext.get("memberIds", String.class);
        String[] memberIds = memberIdsStr.split(",");
        Integer type = requestContext.get("type", Integer.class);
        String userId = subject.getCurrentUserId();
        boolean flag = settlementService.findSettlementCount(classId) == 0 ? false : true;
        int success = 0;
        List<String> successIds = new ArrayList<>();

        for (int i = 0; i < memberIds.length; i++) {

            int ret = flag ? traineeService.addTrainee(classId, type, memberIds[i], userId, Optional.of(1),Optional.empty()) :
                    traineeService.addTrainee(classId, type, memberIds[i], userId, Optional.empty(),Optional.empty());

            if (ret == 1) {
                success++;
                successIds.add(memberIds[i]);
            }
        }

        if (success > 0) {
            Integer num = traineeService.countTrainee(classId);
            classInfoService.updateTraineeNum(classId, num, false, false, false);
            memberCache.clear(Trainee.CACHE_TRAINEE_MEMBER_COURSE_KEY + classId);
            studyCache.clear(Trainee.CACHE_TRAINEE_STUDY_TIME_KEY + classId);
            if (type.equals(0)) {
                signDetailService.insertDetail(classId, successIds.toArray(new String[0]));
            }
        }
        int fail = memberIds.length - success;
        return new int[]{success, fail};
    }

    /**
     * 学员审核
     *
     * @param requestContext
     * @return
     */
    @RequestMapping(value = "/audit/{ids}", method = RequestMethod.PUT)
    @Param(name = "ids", type = String.class, required = true)
    @Param(name = "auditStatus", type = Integer.class, required = true)
    @Param(name = "auditOpinion", type = String.class)
    @Param(name = "classId", type = String.class, required = true)
    @Param(name = "quotaType", type = Integer.class, required = true)
    @Param(name = "className", type = String.class, required = true)
    @JSON("*")
    public int[] auditAll(RequestContext requestContext, Subject<Member> subject) {
        com.zxy.product.human.entity.Organization organization = memberService
                .getCompanyOrganizationWithLevel2ByMemberId(subject.getCurrentUserId());
        String idsStr = requestContext.get("ids", String.class);
        String classId = requestContext.get("classId", String.class);
        Integer auditStatus = requestContext.get("auditStatus", Integer.class);
        Optional<String> auditOpinion = requestContext.getOptional("auditOpinion", String.class);
        String traineeAuditOpinion = auditOpinion.orElse("无");
        Integer quotaType = requestContext.get("quotaType", Integer.class);
        String className = requestContext.get("className", String.class);
        String[] params = {className,traineeAuditOpinion};
        String[] ids = idsStr.split(",");
        // 审核成功的ids
        List<String> agreeIds = new ArrayList<>();
        List<String> agreeTepIds = new ArrayList<>();
        String[] memberIds = new String[ids.length];

        // 审核失败的ids
        List<String> refuseIds = new ArrayList<>();
        List<String> refuseTepIds = new ArrayList<>();
        int success = 0;
        for (int i = 0;i<ids.length;i++) {
            Trainee trainee = traineeService.updateStatus(ids[i], auditStatus, auditOpinion, quotaType);
            if (trainee != null) {
                if (trainee.getAuditStatus() == Trainee.AUDIT_AGREE) {
                    agreeIds.add(trainee.getMemberId());
                    agreeTepIds.add(trainee.getPhoneNumber());
                    memberIds[i] = trainee.getMemberId();
                }
                if (trainee.getAuditStatus() == Trainee.AUDIT_REFUSE) {
                    refuseIds.add(trainee.getMemberId());
                    refuseTepIds.add(trainee.getPhoneNumber());
                }
                success++;
            }
        }
        Integer num = traineeService.countTrainee(classId);

        classInfoService.updateTraineeNum(classId, num,false,false,false);

        // 发送站内消息,审核通过消息
        if (agreeIds.size() > 0) {
            String[] agreeIdss = new String[agreeIds.size()];
            String[] agreeTepIdss = new String[agreeTepIds.size()];
            memberCache.clear(Trainee.CACHE_TRAINEE_MEMBER_COURSE_KEY + classId);
            studyCache.clear(Trainee.CACHE_TRAINEE_STUDY_TIME_KEY + classId);
            for (int i = 0; i < agreeIds.size(); i++) {
                agreeIdss[i] = agreeIds.get(i);
            }
            for (int i = 0; i < agreeTepIds.size(); i++) {
            	agreeTepIdss[i] = agreeTepIds.get(i);
            }
            Set<String> set = new HashSet<>();
            for(int i=0;i<agreeTepIdss.length;i++){
                set.add(agreeTepIdss[i]);
            }
            String[] arrayResult = set.toArray(new String[set.size()]);
            messageSendService.sendTep(subject.getCurrentUserId(), agreeIdss,arrayResult, MessageConstant.CLASS_SIGN_AUDIT_PASS, Optional.of(classId),
                    Optional.empty(), Optional.of(params));
            for (String id : agreeIds) {
                messageSender.send(MessageTypeContent.CLASS_ADD_TRAINEE_BY_CODE,
                        MessageHeaderContent.CLASSID, classId,
                        MessageHeaderContent.MEMBERID, id);
            }

            signDetailService.insertDetail(classId, memberIds);
        }

        // 审核拒绝消息
        if (refuseIds.size() > 0) {
            String[] refuseIdss = new String[refuseIds.size()];
            String[] refuseTepIdss = new String[refuseTepIds.size()];
            for (int i = 0; i < refuseIds.size(); i++) {
                refuseIdss[i] = refuseIds.get(i);
            }
            for (int i = 0; i < refuseTepIds.size(); i++) {
            	refuseTepIdss[i] = refuseTepIds.get(i);
            }
            Set<String> set = new HashSet<>();
            for(int i=0;i<refuseTepIdss.length;i++){
                set.add(refuseTepIdss[i]);
            }
            String[] arrayResult = set.toArray(new String[set.size()]);
            messageSendService.sendTep(subject.getCurrentUserId(), refuseIdss,arrayResult, MessageConstant.CLASS_SIGN_AUDIT_REFUSE, Optional.empty(),
                    Optional.empty(), Optional.of(params));
        }
        int fail = ids.length - success;
        int[] result = {success, fail};
        return result;
    }

    /**
     * 学员排序
     *
     * @param requestContext
     * @return
     */
    @RequestMapping(value = "/update-sort/{id}", method = RequestMethod.PUT)
    @Param(name = "id", type = String.class)
    @Param(name = "sort", type = Integer.class)
    @Param(name = "classId", type = String.class)
    @Param(name = "sortForGroup", type = Integer.class)
    @JSON("*")
    @Audit(module = "活动管理", subModule = "培训管理－班级管理", action = Audit.Action.UPDATE, fisrtAction = "班级维护", secondAction ="成员管理—添加学员", desc = "操作学员排序于班级{0}",ids = {"classId"}, jsons = {"name"}, keys = {"class-info"})
    public Trainee updateSort(RequestContext requestContext) {
        return traineeService.updateSort(requestContext.get("id", String.class),
                requestContext.getOptionalInteger("sort"), requestContext.getOptionalInteger("sortForGroup"));
    }

    /**
     * 学员分组排序
     *
     * @param requestContext
     * @return
     */
    @RequestMapping(value = "/group-sort/{id}", method = RequestMethod.PUT)
    @Param(name = "id")
    @Param(name = "sortForGroup", type = Integer.class)
    @JSON("*")
    public Map<String,String> updateGroupSort(RequestContext requestContext) {
        traineeService.updateGroupSort(requestContext.get("id", String.class),
                                             requestContext.getInteger("sortForGroup"));

        return ImmutableMap.of("code","200");
    }

    /**
     * 修改学员信息
     *
     * @param requestContext
     * @return
     */
    @RequestMapping(value = "/update/{id}", method = RequestMethod.PUT)
    @Param(name = "id", type = String.class, required = true)
    @Param(name = "sex", type = Integer.class, required = true)
    @Param(name = "newCompany" ,type = String.class)
    @Param(name = "newOrganization" ,type = String.class)
    @Param(name = "levelId", type = String.class)
    @Param(name = "nation", type = String.class, required = true)
    @Param(name = "phoneNumber", type = String.class, required = true)
    @Param(name = "email", type = String.class, required = true)
    @Param(name = "remark", type = String.class)
    @Param(name = "jobName", type = String.class)
    @JSON("*")
//    @Audit(module = "活动管理", subModule = "培训管理－班级管理", action = Audit.Action.UPDATE, fisrtAction = "班级维护", secondAction ="成员管理—添加学员", desc = "操作添加学员于班级{1}", ids = {"classId"}, jsons = {"name"}, keys = {"class-info"})
    public Trainee update(RequestContext requestContext) {
        return traineeService.update(requestContext.getString("id"), requestContext.getInteger("sex"),requestContext.getOptionalString("newCompany"),
        		requestContext.getOptionalString("newOrganization"),requestContext.getOptionalString("levelId"), requestContext.getString("nation"), requestContext.getString("phoneNumber"),
                requestContext.getString("email"), requestContext.getOptionalString("remark"),requestContext.getOptionalString("jobName")
        );
    }

    /**
     * 删除学员
     *
     * @param requestContext
     * @return
     */
    @RequestMapping(value = "/{id}", method = RequestMethod.POST)
    @Param(name = "id", type = String.class, required = true)
    @Param(name = "name")
    @Param(name = "className", type = String.class)
    @Param(name = "type", type = Integer.class)
    @Param(name = "classId", type = String.class)
    @JSON("*")
    @Audit(module = "活动管理", subModule = "培训管理－班级管理", action = Audit.Action.DELETE, fisrtAction = "班级维护", secondAction ="成员管理—删除学员", desc = "操作删除正式成员{0}于班级{1}",params = {"name","className"},businessType="type", businessValue="0")
    @Audit(module = "活动管理", subModule = "培训管理－班级管理", action = Audit.Action.DELETE, fisrtAction = "班级维护", secondAction ="成员管理—删除学员", desc = "操作删除非正式成员{0}于班级{1}",params = {"name","className"},businessType="type", businessValue="1")
    public int delete(RequestContext requestContext, Subject<Member> subject) {
        String classId = requestContext.get("classId", String.class);
        Integer type = requestContext.get("type", Integer.class);
        boolean flag = false;
        boolean flag1 = false;
        boolean flag2 = false;
        Trainee selectTrainee = traineeService.getTrainee(requestContext.get("id", String.class));
        if (selectTrainee != null && selectTrainee.getMemberId() != null && !("").equals(selectTrainee.getMemberId())) {
            messageRecordService.delete(classId, selectTrainee.getMemberId());
        }
        if(selectTrainee.getCommitFourDegrees()!=null&& selectTrainee.getCommitFourDegrees().equals(1)){
            flag = true;
        }
        if(selectTrainee.getCommitAbility()!=null&&selectTrainee.getCommitAbility().equals(1)){
            flag1 = true;
        }
        if(selectTrainee.getCommitSuperiorLeadership()!=null&& selectTrainee.getCommitSuperiorLeadership().equals(1)){
            flag2 = true;
        }
        boolean deleteFlag = settlementService.findSettlementCount(classId)==0?false:true;
        int memberNum = traineeService.delete(selectTrainee,requestContext.get("id", String.class),deleteFlag);
        Integer num = traineeService.countTrainee(classId);
        traineeService.delete(classId,8);
        classInfoService.updateTraineeNum(classId, num,flag,flag1,flag2);
        classEvaluateService.deleteClassProgress(classId, selectTrainee.getMemberId());
        signDetailService.deleteMember(classId, selectTrainee.getMemberId(),type);
        signDetailService.deleteMemberLeve(classId, selectTrainee.getMemberId());
        memberCache.clear(Trainee.CACHE_TRAINEE_MEMBER_COURSE_KEY + classId);
        studyCache.clear(Trainee.CACHE_TRAINEE_STUDY_TIME_KEY + classId);
        return memberNum;
    }

    /**
     * 响应中心删除学员
     *
     * @param requestContext
     * @return
     */
    @RequestMapping(value = "/response/{id}", method = RequestMethod.POST)
    @Param(name = "id", type = String.class, required = true)
    @Param(name = "classId", type = String.class, required = true)
    @JSON("*")
//    @Audit(module = "活动管理", subModule = "培训管理－班级管理", action = Audit.Action.DELETE, fisrtAction = "班级维护", secondAction ="成员管理—删除学员", desc = "操作删除学员{0}于班级{1}",params = {"name"},ids = {"classId"}, jsons = {"name"}, keys = {"class-info"})
    public int deleteByresponse(RequestContext requestContext, Subject<Member> subject) {
//        Trainee trainee = traineeService.delete(requestContext.get("id", String.class));
    	String classId = requestContext.get("classId", String.class);
        boolean flag = false;
        boolean flag1 = false;
        boolean flag2 = false;
        Trainee selectTrainee = traineeService.getTrainee(requestContext.get("id", String.class));
        if (selectTrainee != null && selectTrainee.getMemberId() != null && !("").equals(selectTrainee.getMemberId())) {
            messageRecordService.delete(classId, selectTrainee.getMemberId());
        }
//        if(selectTrainee.getCommitFourDegrees()!=null&& selectTrainee.getCommitFourDegrees().equals(1)){
//            flag = true;
//        }
//        if(selectTrainee.getCommitAbility()!=null&&selectTrainee.getCommitAbility().equals(1)){
//            flag1 = true;
//        }
//        if(selectTrainee.getCommitSuperiorLeadership()!=null&& selectTrainee.getCommitSuperiorLeadership().equals(1)){
//            flag2 = true;
//        }
        int memberNum = traineeService.delete(selectTrainee,requestContext.get("id", String.class),false);
        traineeService.delete(classId,8);
        Integer num = traineeService.countTrainee(classId);
        classInfoService.updateTraineeNum(classId, num,flag,flag1,flag2);
        classEvaluateService.deleteClassProgress(classId, selectTrainee.getMemberId());
        signDetailService.deleteMember(classId, selectTrainee.getMemberId(),Trainee.TRAINEE_TYPE_FORMAL);
        signDetailService.deleteMemberLeve(classId, selectTrainee.getMemberId());
        memberCache.clear(Trainee.CACHE_TRAINEE_MEMBER_COURSE_KEY + classId);
        studyCache.clear(Trainee.CACHE_TRAINEE_STUDY_TIME_KEY + classId);
        return memberNum;
    }

    /**
     * 导出正式学员
     *
     * @param context
     * @param subject
     * @throws IOException
     */
    @RequestMapping(value = "/download", method = RequestMethod.GET)
    @Param(name = "page", type = Integer.class, required = true)
    @Param(name = "pageSize", type = Integer.class, required = true)
    @Param(name = "classId", type = String.class, required = true)
    @Param(name = "auditStatus", type = Integer.class, required = true)
    @Param(name = "memberName", type = String.class)
    @Param(name = "memberFullName", type = String.class)
    @Param(name = "organizationName", type = String.class)
    @Param(name = "register", type = Integer.class)
    @Param(name = "sortType", type = Integer.class)
    @Audit(module = "活动管理", subModule = "培训管理－班级管理", action = Audit.Action.EXPORT, fisrtAction = "班级维护", secondAction ="成员管理—导出学员", desc = "操作导出学员于班级{0}",ids = {"classId"}, jsons = {"name"}, keys = {"class-info"})
    public void download(RequestContext context, Subject<Member> subject) throws IOException {

        String classId = context.get("classId", String.class);
        ClassInfo classInfo = classInfoService.findClassAndProjectByClassId(classId);

        HttpServletResponse response = context.getResponse();
        response.setContentType("application/octet-stream;charset=utf-8");
        response.setHeader("Content-Disposition",
                "attachment;filename=" + new String(("《" + classInfo.getClassName() + "》" + "培训学员信息").getBytes("gb2312"), "ISO-8859-1") + ".xls");

        List<Trainee> list = this.findformalTrainees(context, subject).getItems();
        int i = 1;
        List<MemberConfig> memList = memberConfigService.list(Optional.of(2));
        Map<String, String> map = new HashMap<>();
        for (Trainee t : list) {
            if(t.getNewCompany()!=null&&!"".equals(t.getNewCompany())){
                t.setCompanyName(t.getNewCompany());
            }
            if(t.getNewOrganization()!=null&&!"".equals(t.getNewOrganization())){
                t.setOrganizationName(t.getNewOrganization());
            }
            for(MemberConfig mc:memList){
                if(mc.getId().equals(t.getNation())){
                    map.put(mc.getId(),mc.getValue());
                }
            }
            t.setIndex(i);
            i++;
        }
        String[] num = {"序号", "姓名", "员工编号", "性别", "民族", "单位", "部门", "职务", "手机", "邮箱","报到情况"};
        Object[][] value = new Object[list.size() + 1][12];
        HSSFWorkbook workbook = new HSSFWorkbook();
        HSSFSheet sheet = workbook.createSheet("学员列表导出");
        OutputStream out = response.getOutputStream();
        for (int m = 0; m < num.length; m++) {
            value[0][m] = num[m];
        }
        for (int j = 0;j<list.size();j++) {
            value[j + 1][0] = list.get(j).getIndex();
            value[j + 1][1] = list.get(j).getMember().getFullName();
            value[j + 1][2] = DesensitizationUtil.desensitizeEmployeeId(list.get(j).getMember().getName());
            value[j + 1][3] = list.get(j).getSex()!=null&&list.get(j).getSex()== 0 ? "男" : "女";
            value[j + 1][4] = map.get(list.get(j).getNation());
            value[j + 1][5] = list.get(j).getCompanyName();
            value[j + 1][6] = list.get(j).getOrganizationName();
            value[j + 1][7] = list.get(j).getMember().getJobName();
            value[j + 1][8] = getDecryptParam(Optional.of(list.get(j).getPhoneNumber()));
            value[j + 1][9] = list.get(j).getEmail();
            value[j + 1][10] = list.get(j).getRegister() == Trainee.NO_SHOW ? "未报到" : "已报到";
        }
        this.downloadWriteArray (workbook, sheet, list.size() + 1, 11, value);
        String date = com.zxy.product.system.util.DateUtil.dateLongToString(System.currentTimeMillis(), com.zxy.product.system.util.DateUtil.YYYYMMDD);
        com.zxy.product.human.entity.Member nameAndFullName = memberService.getNameAndFullName(subject.getCurrentUserId());
        String content = (nameAndFullName.getFullName() + "  " + nameAndFullName.getName() + "  " + date);
        ImportExportUtil.putWaterRemarkToExcel(workbook, workbook.getSheetAt(0), null, 0, 0, 0, 0, 1, 1, 0, 0, content);
        workbook.write(out);
        out.flush();
        out.close();
    }
    public static String convertString(Object value) {
        if (value == null) {
            return "";
        } else {
            return value.toString();
        }
    }
    public static void downloadWriteArray(HSSFWorkbook wb, HSSFSheet sheet, int rows, int cells, Object[][] value) {
        Row row[] = new HSSFRow[rows];
        Cell cell[] = new HSSFCell[cells];
        sheet.setColumnWidth(0, 5 * 256);
        sheet.setColumnWidth(1, 8 * 256);
        sheet.setColumnWidth(2, 12 * 256);
        sheet.setColumnWidth(3, 5 * 256);
        sheet.setColumnWidth(4, 10 * 256);
        sheet.setColumnWidth(5, 15 * 256);
        sheet.setColumnWidth(6, 35 * 256);
        sheet.setColumnWidth(7, 25 * 256);
        sheet.setColumnWidth(8, 15 * 256);
        sheet.setColumnWidth(9, 25 * 256);
        HSSFCellStyle style1 = wb.createCellStyle();
        HSSFCellStyle style = wb.createCellStyle();
        for (int i = 0; i < row.length; i++) {
            row[i] = sheet.createRow(i);
            for (int j = 0; j < cell.length; j++) {
                cell[j] = row[i].createCell(j);
                if (i == 0) {
                    cell[j].setCellValue(convertString(value[i][j]));
                    row[i].setHeightInPoints(25);
                    style1.setBorderBottom(BorderStyle.THIN);//下边框
                    style1.setBorderTop(BorderStyle.THIN);//上边框
                    style1.setBorderLeft(BorderStyle.THIN);//左边框
                    style1.setBorderRight(BorderStyle.THIN);//右边框
                    style1.setFillForegroundColor(HSSFColor.GREY_25_PERCENT.index);
                    style1.setFillPattern(FillPatternType.SOLID_FOREGROUND);
                    style1.setAlignment(HorizontalAlignment.CENTER); //水平居中
                    style1.setVerticalAlignment(VerticalAlignment.CENTER);//垂直居中
                    cell[j].setCellStyle(style1);
                }else{
                    row[i].setHeightInPoints(13);
                    style.setVerticalAlignment(VerticalAlignment.CENTER);//垂直居中
                    cell[j].setCellValue(convertString(value[i][j]));
                    cell[j].setCellStyle(style);
                }
            }
        }
    }

    /**
     * 学习详情
     *
     * @param requestContext
     * @return
     */
    @RequestMapping(value = "/course-study-progresss", method = RequestMethod.GET)
    @Param(name = "classId", type = String.class, required = true)
    @JSON("traineeIds,courseIds")
    public Trainee findCourseStudy(RequestContext requestContext) {
        return traineeService.getCourseStudyProgress(requestContext.get("classId", String.class));
    }

    /**
     * 删除学员分组id
     *
     * @param requestContext
     * @return
     */
    @RequestMapping(value = "/update-group", method = RequestMethod.GET)
    @Param(name = "id", type = String.class, required = true)
    @JSON("*")
    public Trainee updateGroup(RequestContext requestContext) {
        return traineeService.updateGroup(requestContext.get("id", String.class));
    }

    /**
     * 导出分组学员
     *
     * @param context
     * @param subject
     * @throws IOException
     */
    @RequestMapping(value = "/export-group-trainee", method = RequestMethod.GET)
    @Param(name = "page", type = Integer.class, required = true)
    @Param(name = "pageSize", type = Integer.class, required = true)
    @Param(name = "classId", type = String.class, required = true)
    @Param(name = "groupId", type = String.class, required = true)
    public void exportGroupTrainee(RequestContext context, Subject<Member> subject) throws IOException {
        HttpServletResponse response = context.getResponse();
        response.setContentType("application/octet-stream;charset=utf-8");
        response.setHeader("Content-Disposition",
                "attachment;filename=" + new String("分组学员信息".getBytes("gb2312"), "ISO-8859-1") + ".xlsx");
        List<Trainee> list = this.findformalTrainees(context, subject).getItems();
        Writer writer = new ExcelWriter();
        writer.sheet("群组列表", list).field("姓名", trainee -> trainee.getMember().getFullName())
                .field("员工编号", trainee -> trainee.getMember().getName())
                .field("性别", (bean) -> bean.getSex(), x -> x == 0 ? "男" : "女")
                .field("民族", trainee -> trainee.getNationName()).field("单位", trainee -> trainee.getCompanyName())
                .field("部门", trainee -> trainee.getOrganizationName())
                .field("职务", trainee -> trainee.getMember().getJobName())
                .field("手机", trainee -> getDecryptParam(Optional.of(trainee.getPhoneNumber())));
        writer.write(response.getOutputStream());
    }

    /**
     * 导出分组学员
     *
     * @param context
     * @param subject
     * @throws IOException
     */
    @RequestMapping(value = "/export-group-member", method = RequestMethod.GET)
    @Param(name = "page", type = Integer.class, required = true)
    @Param(name = "pageSize", type = Integer.class, required = true)
    @Param(name = "classId", type = String.class, required = true)
    @Param(name = "groupId", type = String.class, required = true)
    public void exportGroupMember(RequestContext context, Subject<Member> subject) throws IOException {
        HttpServletResponse response = context.getResponse();
        response.setContentType("application/octet-stream;charset=utf-8");
        response.setHeader("Content-Disposition",
                           "attachment;filename=" + new String("分组学员信息".getBytes("gb2312"), "ISO-8859-1") + ".xlsx");
        List<Trainee> list = this.findGroupMember(context, subject).getItems();
        Writer writer = new ExcelWriter();
        writer.sheet("群组列表", list).field("姓名", trainee -> trainee.getMember().getFullName())
              .field("员工编号", trainee -> trainee.getMember().getName())
              .field("性别", (bean) -> bean.getSex(), x -> x == 0 ? "男" : "女")
              .field("民族", trainee -> trainee.getNationName()).field("单位", trainee -> trainee.getCompanyName())
              .field("部门", trainee -> trainee.getOrganizationName())
              .field("职务", trainee -> trainee.getMember().getJobName())
              .field("手机", trainee -> getDecryptParam(Optional.of(trainee.getPhoneNumber())));
        writer.write(response.getOutputStream());
    }

    /**
     * 个人中心我的档案 -- 班级
     *
     * @param requestContext
     * @return
     */
    @RequestMapping(value = "person-list", method = RequestMethod.GET)
    @Param(name = "page", type = Integer.class, required = true)
    @Param(name = "pageSize", type = Integer.class, required = true)
    @Param(name = "startTime", type = Long.class)
    @Param(name = "endTime", type = Long.class)
    @JSON("recordCount")
    @JSON("items.(id,organizationName,projectName,startTime,endTime,auditStatus,classStatus)")
    @JSON("more")
    public Map<String,Object> personPage(RequestContext requestContext, Subject<Member> subject) {
        List<String> list =  memberService.getMembersByUserId(subject.getCurrentUserId());
        int pageSwitch = ruleConfigService.getByName("1", RuleConfig.KEY.PC_PAGE_STYLE).map(x-> Integer.parseInt(x.getValue())).orElse(0);
        return traineeService.personPage(
                requestContext.get("page", Integer.class),
                requestContext.get("pageSize", Integer.class),
                requestContext.getOptional("startTime", Long.class),
                requestContext.getOptional("endTime", Long.class),
                list,pageSwitch == 1
                );
    }

    /**
     * 导出个人中心我的档案 -- 班级--查所有
     *
     * @param requestContext
     * @return
     */
    @RequestMapping(value = "/export-person-list", method = RequestMethod.GET)
    @Param(name = "startTime", type = Long.class)
    @Param(name = "endTime", type = Long.class)
    public void export(RequestContext requestContext, Subject<Member> subject) throws IOException {
        HttpServletResponse response = requestContext.getResponse();
        String attachmentName = "我的班级.xlsx";
        if (BrowserUtil.isMSBrowser(requestContext.getRequest().getHeader("User-Agent"))) {
            attachmentName = URLEncoder.encode(attachmentName, "UTF-8");
        } else {
            attachmentName = new String(attachmentName.getBytes("UTF-8"), "ISO-8859-1");
        }
        response.setContentType("application/octet-stream;charset=utf-8");
        response.setHeader("Content-Disposition", "attachment;filename=" + attachmentName);
        List<Trainee> list = traineeService.personAll(
                subject.getCurrentUserId(),  requestContext.getOptional("startTime", Long.class),
                requestContext.getOptional("endTime", Long.class));
        int i = 1;
        for (Trainee t : list) {
            t.setIndex(i);
            i++;
        }
        Writer writer = new ExcelWriter();
        //班级状态（1未实施、2实施中、3已实施）
        Map<Integer, String> classStatusMap = new HashMap<>();
        classStatusMap.put(1, "未实施");
        classStatusMap.put(2, "实施中");
        classStatusMap.put(3, "已实施");
        //审核状态： 0待审核(默认) 1通过 2拒绝
        Map<Integer, String> auditStatusMap = new HashMap<>();
        auditStatusMap.put(1, "待开始");
        auditStatusMap.put(2, "被拒绝");
        auditStatusMap.put(3, "已参训");
        writer.sheet("我的班级", list)
                .field("序号", Trainee::getIndex)
                .field("班级名称", Trainee::getProjectName)
                .field("主办方", Trainee::getOrganizationName)
                .field("报道日", (m) -> m.getStartTime(), x -> x == null ? "" : DateUtil.dateLongToString(x, DateUtil.YYYY_MM_DD))
                .field("返程日", (m) -> m.getEndTime(), x -> x == null ? "" : DateUtil.dateLongToString(x, DateUtil.YYYY_MM_DD))
                .field("班级状态", m -> classStatusMap.get(m.getClassStatus()))
                .field("参训状态", m -> auditStatusMap.get(m.getNum()));
        writer.write(response.getOutputStream());
    }

    @RequestMapping(value = "/members", method = RequestMethod.GET)
    @Param(name = "classId", type = String.class, required = true)
    @Param(name = "type", type = Integer.class, required = true)
    @JSON("*")
    public String[] findMemberIds(RequestContext context, Subject<Member> subject) {
        return traineeService.findMemberIds(context.getString("classId"), context.getInteger("type"));
    }

    @RequestMapping(value = "/save-group-manage", method = RequestMethod.POST)
    @Param(name = "classId", type = String.class, required = true)
    @Param(name = "groupId", type = String.class, required = true)
    @Param(name = "ids", type = String.class)
    @Param(name = "fmTrainees", type = String.class)
    @JSON("*")
    public int saveGroupManage(RequestContext context, Subject<Member> subject) {
        return traineeService.saveGroupManage(context.getString("classId"), context.getString("groupId"),
                context.getOptionalString("ids"), context.getOptionalString("fmTrainees"));
    }

    @RequestMapping(value = "/current-trainee", method = RequestMethod.GET)
    @Param(name = "type", type = Integer.class, required = true)
    @Param(name = "classId", type = String.class, required = true)
    @Param(name = "auditStatus", type = Integer.class)
    @JSON("*")
    public Trainee findByCurrentUserId(RequestContext context, Subject<Member> subject) {
        return traineeService.findByMemberId(subject.getCurrentUserId(), context.getInteger("type"),
                context.getString("classId"), context.getOptional("auditStatus", Integer.class)).orElse(null);
    }

    @RequestMapping(value = "/find-by-memberId", method = RequestMethod.GET)
    @Param(name = "type", type = Integer.class, required = true)
    @Param(name = "memberId", type = String.class, required = true)
    @Param(name = "classId", type = String.class, required = true)
    @JSON("*")
    public Trainee findByMemberId(RequestContext context) {
        return traineeService.findByMemberId(context.getString("memberId"), context.getInteger("type"),
                context.getString("classId"), Optional.empty()).orElse(null);
    }

    /**
     * 学员报名
     *
     * @param context
     * @param subject
     * @return
     */
    @RequestMapping(value = "/sign-up", method = RequestMethod.POST)
    @Param(name = "memberId", type = String.class, required = true)
    @Param(name = "phoneNumber", type = String.class, required = true)
    @Param(name = "email", type = String.class, required = true)
    @Param(name = "sex", type = Integer.class, required = true)
    @Param(name = "levelId", type = String.class)
    @Param(name = "nation", type = String.class, required = true)
    @Param(name = "remark", type = String.class)
    @Param(name = "jobName", type = String.class)
    @Param(name = "classId", type = String.class, required = true)
    @Param(name = "organizationId", type = String.class, required = true)
    @Param(name = "settleOrganizationId", type = String.class)
    @Param(name = "className")
    @Param(name = "mainlandPersonnel" , type = Integer.class,required = true)
    @Param(name = "signupCode", type = String.class) //校验报名码
    @JSON("id,memberId,type,phoneNumber,email,sex,levelId,nation,remark,auditStatus,"
            + "auditOption,classId,createTime,settleOrganizationId")
    public Trainee signUp(RequestContext context, Subject<Member> subject) {
        // 接收参数
        String memberId = context.getString("memberId");
        String phoneNumber = context.getString("phoneNumber");
        String email = context.getString("email");
        Integer sex = context.getInteger("sex");
        Optional<String> levelId = context.getOptional("levelId", String.class);
        String nation = context.getString("nation");
        String classId = context.getString("classId");
        String organizationId = context.getString("organizationId");
        Optional<String> settleOrganizationId = context.getOptional("settleOrganizationId", String.class);
        Optional<String> remark = context.getOptionalString("remark");
        Optional<String> jobName = context.getOptionalString("jobName");
        String className = context.getString("className");
        Integer mainlandPersonnel = context.getInteger("mainlandPersonnel");

        Optional<String> signupCodeOpt = context.getOptionalString("signupCode");
        Optional<ClassSignupInfo>  classSignupInfo = signupService.findByClassId(classId);
        if(classSignupInfo.isPresent() && ClassSignupInfo.IS_OPEN_TRUE == classSignupInfo.get().getIsOpen() && !signupCodeOpt.isPresent()){
            throw new UnprocessableException(ErrorCode.TraineeSignCodeIsError);
        }
        if(classSignupInfo.isPresent() && signupCodeOpt.isPresent()){
            if(ClassSignupInfo.IS_OPEN_TRUE == classSignupInfo.get().getIsOpen()
                    && !signupCodeOpt.get().equals(classSignupInfo.get().getSignupCode())){ //需要报名但报名码不对的情况
                throw new UnprocessableException(ErrorCode.TraineeSignCodeIsError);
            }
        }
        // 防止重复提交
        String signupCache = studyCache.get("signup_member_" + classId + memberId, String.class);
		if (signupCache == null) {
			studyCache.set("signup_member_" + classId + memberId, "Y", 60);
		} else {
			return new Trainee();
		}
        // 学员报名
        Trainee trainee = traineeService.signUp(memberId, phoneNumber, email, sex, levelId, nation, classId,
                organizationId, settleOrganizationId, subject.getCurrentUserId(), remark,jobName,Optional.of(mainlandPersonnel));
        // 如果报名成功且状态为待审核通知当前班级班务人员做审核
        if (trainee != null && trainee.getAuditStatus() == Trainee.AUDIT_WAIT) {
            // 查询当前班级班务人员
            PagedResult<ClassstaffClass> ccs = classstaffClassService.find(1, 20, classId);
            List<ClassstaffClass> ccList = ccs.getItems();
            List<String> receiveIds = new ArrayList<>();
            // 获取班务人员的memberIds
            ccList.forEach(x -> {
                receiveIds.add(x.getMember().getId());
            });
            String[] params = {className};
            if (receiveIds != null && receiveIds.size() > 0) {
                String[] re = new String[receiveIds.size()];
                for (int i = 0; i < receiveIds.size(); i++) {
                    re[i] = receiveIds.get(i);
                }
                ClassInfo classInfo = classInfoService.get(classId);
                // 获取当前用户所属公司
//			    com.zxy.product.human.entity.Organization organization = memberService
//			            .getCompanyOrganizationWithLevel2ByMemberId(subject.getCurrentUserId());
                // 发送站内消息，邮件，短信
                messageSendService.send(subject.getCurrentUserId(), re, MessageConstant.CLASS_SIGN_UP, Optional.of(classInfo.getProjectId()),
                        Optional.empty(), Optional.of(params));
            }
        }
        if (trainee != null && trainee.getAuditStatus() == Trainee.AUDIT_AGREE) {
            memberCache.clear(Trainee.CACHE_TRAINEE_MEMBER_COURSE_KEY + classId);
            studyCache.clear(Trainee.CACHE_TRAINEE_STUDY_TIME_KEY + classId);
        }
        studyCache.clear("signup_member_" + classId + memberId);
        return trainee;
    }

    @RequestMapping(value = "/find-trainee-by-class-id", method = RequestMethod.GET)
    @Param(name = "classId", type = String.class, required = true)
    @JSON("*")
    public Trainee findTraineeByClassId(RequestContext requestContext, Subject<Member> subject) {
        Trainee trainee = traineeService.findTraineeByClassId(requestContext.get("classId", String.class),
                subject.getCurrentUserId());
        return trainee;
    }

    @RequestMapping(value = "/find-trainee-by-class-id-count", method = RequestMethod.GET)
    @Param(name = "classId", type = String.class, required = true)
    @JSON("*")
    public Integer findTraineeByClassId(RequestContext requestContext) {
        List<Trainee> traineeList = traineeService.findTraineeByClassId(requestContext.get("classId", String.class));
        return traineeList.size();
    }

    /**
     * 学员报名(通过报名链接)
     *
     * @param context
     * @return
     */
    @RequestMapping(value = "/sign-up-by-phone", method = RequestMethod.POST)
    @Param(name = "memberId", type = String.class, required = true)
    @Param(name = "phoneNumber", type = String.class, required = true)
    @Param(name = "email", type = String.class, required = true)
    @Param(name = "sex", type = Integer.class, required = true)
    @Param(name = "levelId", type = String.class)
    @Param(name = "nation", type = String.class, required = true)
    @Param(name = "remark", type = String.class)
    @Param(name = "jobName", type = String.class)
    @Param(name = "classId", type = String.class, required = true)
    @Param(name = "organizationId", type = String.class, required = true)
    @Param(name = "settleOrganizationId", type = String.class)
    @Param(name = "className", type = String.class, required = true)
//    @Param(name = "jobName", type = String.class, required = true)
    @Param(name = "mainlandPersonnel" , type = Integer.class,required = true)
    @JSON("id,memberId,type,phoneNumber,email,sex,levelId,nation,remark,auditStatus,"
            + "auditOption,classId,createTime,settleOrganizationId")
    public Trainee signUpWithoutLogin(RequestContext context) {
        // 接收参数
        String memberId = context.getString("memberId");
        String phoneNumber = context.getString("phoneNumber");
        String email = context.getString("email");
        Integer sex = context.getInteger("sex");
        Optional<String> levelId = context.getOptional("levelId", String.class);
        String nation = context.getString("nation");
        String classId = context.getString("classId");
        String organizationId = context.getString("organizationId");
        Optional<String> settleOrganizationId = context.getOptional("settleOrganizationId", String.class);
        Optional<String> remark = context.getOptionalString("remark");
        Optional<String> jobName = context.getOptionalString("jobName");
        String className = context.getString("className");
        Optional<String> licensePlateNumber = context.getOptional("licensePlateNumber",String.class);
        Integer mainlandPersonnel = context.getInteger("mainlandPersonnel");


        // 防止重复提交
        String signupCache = studyCache.get("signup_member_" + classId + memberId, String.class);
		if (signupCache == null) {
			studyCache.set("signup_member_" + classId + memberId, "Y", 60);
		} else {
			return new Trainee();
		}
        // 学员报名
        Trainee trainee = traineeService.signUp(memberId, phoneNumber, email, sex, levelId, nation, classId,
                organizationId, settleOrganizationId, memberId, remark,jobName,Optional.of(mainlandPersonnel));
        // 如果报名成功且状态为待审核通知当前班级班务人员做审核
        if (trainee != null && trainee.getAuditStatus() == Trainee.AUDIT_WAIT) {
            // 查询当前班级班务人员
            PagedResult<ClassstaffClass> ccs = classstaffClassService.find(1, 20, classId);
            List<ClassstaffClass> ccList = ccs.getItems();
            List<String> receiveIds = new ArrayList<>();
            // 获取班务人员的memberIds
            ccList.forEach(x -> {
                receiveIds.add(x.getMember().getId());
            });
            String[] params = {className};
            if (receiveIds != null && receiveIds.size() > 0) {
                String[] re = new String[receiveIds.size()];
                for (int i = 0; i < receiveIds.size(); i++) {
                    re[i] = receiveIds.get(i);
                }
                // 获取当前用户所属公司
//              com.zxy.product.human.entity.Organization organization = memberService
//                      .getCompanyOrganizationWithLevel2ByMemberId(subject.getCurrentUserId());
                // 发送站内消息，邮件，短信
                messageSendService.send(memberId, re, MessageConstant.CLASS_SIGN_UP, Optional.of(classId),
                        Optional.empty(), Optional.of(params));
            }

        }
        if (trainee != null && trainee.getAuditStatus() == Trainee.AUDIT_AGREE) {
            memberCache.clear(Trainee.CACHE_TRAINEE_MEMBER_COURSE_KEY + classId);
            studyCache.clear(Trainee.CACHE_TRAINEE_STUDY_TIME_KEY + classId);
        }
        studyCache.clear("signup_member_" + classId + memberId);
        return trainee;
    }

    /**
     * 查询制定班级的正式成员的人数
     *
     * @param requestContext
     * @return
     */
    @RequestMapping(value = "/find-trainee-num", method = RequestMethod.GET)
    @Param(name = "classId", type = String.class, required = true)
    @Param(name = "ids", type = String.class, required = true)
    @JSON("*.*")
    public Integer findFormalTrainee(RequestContext requestContext) {
    	Integer count = 0;
        String ids = requestContext.get("ids", String.class);
        String[] split = ids.split(",");
        String[] s = {split[0]};
        String[] byOrgId = groupConfigurationValueService.getByOrgId(s);
        if (byOrgId != null && byOrgId.length > 0) {
        	count = traineeService.countFormalTrainee(
                    requestContext.get("classId", String.class),
                    byOrgId);
        } else {
        	count = traineeService.countFormalTrainee(
                    requestContext.get("classId", String.class),
                    split);
        }
        return count;
    }

    /**
     * 根据班级Id查询该班级的正式成员的人数
     *
     * @param context
     * @return
     */
    @RequestMapping(value = "/count-trainee-num", method = RequestMethod.GET)
    @Param(name = "classId", type = String.class, required = true)
    @JSON("*.*")
    public Integer countFormalTraineeByClassId(RequestContext context) {
        Integer count = traineeService.countFormalTraineeByClassId(context.get("classId", String.class));
        return count;
    }
    @RequestMapping(value = "/find-message", method = RequestMethod.GET)
    @Param(name = "classId", type = String.class, required = true)
    @JSON("receiver")
    public List<MessageRecord> findMessage(RequestContext context){
        List<MessageRecord> messageRecordList = traineeService.messageMemberIds(context.get("classId", String.class));
        return messageRecordList;
    }
    
    @RequestMapping(value = "/no-group", method = RequestMethod.GET)
    @Param(name = "classId", type = String.class, required = true)
    @JSON("*")
    public int findNoGrouping(RequestContext context){
    	return traineeService.getNoGrouping(context.get("classId", String.class));
    }

    /**
     * 手动初始化所有学员的系统通知消息满意度相关
     * @param context
     * @return
     */
    @RequestMapping(value = "/hand-init", method = RequestMethod.GET)
    @Permitted
    @Param(name = "classId", type = String.class)
    @Param(name = "memberId", type = String.class)
    @JSON("*")
    public int handInit(RequestContext context){
        Optional<String> classId=context.getOptionalString("classId");
        if(classId.isPresent()){
            //发送待办消息
            messageSender.send(MessageTypeContent.AGENCY_CLASSID, MessageHeaderContent.CLASSID,classId.get());
        }
        Optional<String> memberId=context.getOptionalString("memberId");
        if(memberId.isPresent()){
            //发消息，更新学员的待办消息
            messageSender.send(
                    MessageTypeContent.SUBMIT_RESEARCH_QUESTIONARY_NEW,MessageHeaderContent.MEMBERID,
                    memberId.get());
        }
        int flag=1;
        int page=1;
        int pageSize=200;
        while(flag>0){
            List<String> subList=traineeService.getTraineeList((page-1)*pageSize,pageSize);
            for(String mid:subList){
                //发消息，更新学员的待办消息
                messageSender.send(
                        MessageTypeContent.SUBMIT_RESEARCH_QUESTIONARY_NEW,MessageHeaderContent.MEMBERID,
                        mid);
            }
            if(subList==null||subList.isEmpty()||subList.size()<pageSize){
                flag=0;
            }
            page++;
        }
        return page;
    }

    @RequestMapping(value = "/sort-template",method= RequestMethod.GET)
    @Permitted
    @Param(name = "classId", required = true)
    public void getSortTemplate(RequestContext requestContext) {
        try {
            HttpServletResponse response = getHttpServletResponse(requestContext, TEMPLATE_NAME);
            Writer writer = new ExcelWriter();

            PagedResult<Trainee> traineeList = getTrainees(requestContext.getString("classId"));

            List<Trainee> items = traineeList.getItems();

            writeSheet(writer, items);

            writer.write(response.getOutputStream());
            
        } catch (UnsupportedEncodingException e) {
            throw new RuntimeException(e);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    private PagedResult<Trainee> getTrainees(String classId) {
        return traineeService.find(1, 3000, classId,
                                   Optional.of(Trainee.TRAINEE_TYPE_FORMAL), Optional.empty(),
                                   Optional.empty(), Optional.empty(),
                                   Optional.empty(), Optional.empty(),
                                   Optional.of(1), Optional.empty(),
                                   Optional.empty(), Optional.empty(), Optional.of(1));
    }

    private void writeSheet(Writer writer, List<Trainee> items) {
        if (CollectionUtils.isEmpty(items)) {
            writer.sheet(TEMPLATE_NAME, new ArrayList<>())
                  .field("手动排序（填写1~3000范围）", x -> "")
                  .field("姓名", x -> "")
                  .field("员工编号（必填）", x -> "")
                  .field("单位", x -> "")
                  .field("部门", x -> "")
                  .field("职务", x -> "");
        } else {
            writer.sheet(TEMPLATE_NAME, items)
                  .field("手动排序（填写1~3000范围）",  x -> Objects.isNull(x.getSortNew()) ? "" : x.getSortNew())
                  .field("姓名", x -> Optional.ofNullable(x.getMember()).map(Member::getFullName).orElse(""))
                  .field("员工编号（必填）", x -> Optional.ofNullable(DesensitizationUtil.desensitizeEmployeeId(x.getMember().getName())).orElse(""))
                  .field("单位", x -> StringUtils.isEmpty(x.getCompanyName()) ? "" : x.getCompanyName())
                  .field("部门", x -> StringUtils.isEmpty(x.getOrganizationName()) ? "" : x.getOrganizationName())
                  .field("职务", x -> StringUtils.isEmpty(x.getMember().getJobName()) ? "" : x.getMember().getJobName());
        }
    }

    @RequestMapping(value = "/sort", method = RequestMethod.POST)
    @Param(name="classId", required=true)
    @JSON("successCount, errorCount, errorFileId")
    @JSON("data.(*)")
    @JSON("errorRow.(row,column,code)")
    @Permitted
    public Map<String, Object> importSortFile(@RequestParam("file") MultipartFile file,RequestContext context){
        String classId = context.getString("classId");
        List<Trainee> traineeList = traineeService.getTraineeByClassId(classId);

        Map<String, Object> map = new HashMap<>();

        //当前培训班下无正式人员
        if (CollectionUtils.isEmpty(traineeList)) {
            throw new UnprocessableException(ErrorCode.TraineeMemberIsEmpty);
        }
        Map<String, Trainee> memberNameMap = traineeList.stream().collect(Collectors.toMap(Trainee::getMemberName, Function.identity(), (v1, v2) -> v1));

        Reader reader = validatorColumn(memberNameMap);

        Reader.Result result;

        try {
            result = reader.read(file.getInputStream());
        } catch (IOException e) {
            throw new UnprocessableException(ErrorCode.EXCEL_NOTSUPPORT);
        }


        if (result.getErrorRows().size() == 0 && result.getCorrectRows().size() == 0) {
            throw new UnprocessableException(ErrorCode.ImportNullFile);
        }
        if (!result.isCellMatched()) {
            throw new UnprocessableException(ErrorCode.InvalidExcelTemplate);
        }
        //读取excel中符合条件的数据
        List<Trainee> excelRecord = readExcel(result, memberNameMap);

        traineeService.updateSort(excelRecord.stream().filter(trainee -> trainee.getId() != null).collect(Collectors.toList()));

        fillResultMap(map, result, excelRecord);

        return map;
    }

    private Reader validatorColumn(Map<String, Trainee> memberNameMap) {
        return new DefaultReader()
                .skipRows(1)
                //排序number
                .setColumn(0, String.class, new TrueValidator<String>().compose((v, vc, previous) -> {
                    return validatorNum(v, vc);
                }))
                .setColumn(1, String.class, new TrueValidator<String>())
                //员工编号
                .setColumn(2, String.class, new RequiredValidator<String>().compose((v, vc, previous) -> {
                    return validatorMemberName(memberNameMap, v, vc);
                }))
                .setColumn(3, String.class, new TrueValidator<String>())
                .setColumn(4, String.class, new TrueValidator<String>())
                .setColumn(5, String.class, new TrueValidator<String>());
    }

    private void fillResultMap(Map<String, Object> map, Reader.Result result, List<Trainee> excelRecord){
        try {
            map.put("successCount", excelRecord.size());
            map.put("errorCount", result.getErrorRows().size());
            map.put("errorRow", fillErrorRow(result));
            map.put("errorFileId", createErrorTempFile(result, createErrorTemplate(result)));
            map.put("data", excelRecord);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }

    }

    private List<ImmutableMap<String, Integer>> fillErrorRow(Reader.Result result) {
        return result.getErrorRows().stream()
                     .flatMap(e -> e.getErrors().stream())
                     .map(error -> ImmutableMap.of("column", error.getColumn(), "row", error.getRow(), "code",
                                                   error.getCode().getCode()))
                     .collect(Collectors.toList());
    }

    private Supplier<Writer> createErrorTemplate(Reader.Result result) {
        return () -> {
            Writer writer = new ExcelWriter();
            writer.sheet(TEMPLATE_NAME, result.getErrorRows())
                  .field("手动排序（填写1~3000范围）", x -> getRowValue(x, 0))
                  .field("姓名", x -> getRowValue(x, 1))
                  .field("员工编号（必填）", x -> getRowValue(x, 2))
                  .field("单位", x -> getRowValue(x, 3))
                  .field("部门", x -> getRowValue(x, 4))
                  .field("职务", x -> getRowValue(x, 4));
            return writer;
        };
    }

    private boolean validatorMemberName(Map<String, Trainee> memberNameMap, String v, ValidateContext vc) {
        if (!memberNameMap.containsKey(v)) {
            vc.error(ErrorCode.MemberCodeError);
            return false;
        }
        return true;
    }

    private List<Trainee> readExcel(Reader.Result result, Map<String, Trainee> memberNameMap) {

        return result.getCorrectRows().stream().map(row -> {
            Trainee trainee = new Trainee();

            String sortNum = row.get(0, String.class);
            if (StringUtils.isEmpty(sortNum)) {
                trainee.setSortNew(null);
            } else {
                trainee.setSortNew(Integer.valueOf(sortNum));
            }

            String memberName = row.get(2, String.class);
            trainee.setId(memberNameMap.get(memberName).getId());
            return trainee;
        }).collect(Collectors.toList());
    }

    private boolean validatorNum(String v, ValidateContext vc) {
        if (v == null || v.length() == 0) {
            return true;
        }
        if (!Pattern.matches("^\\d+$", v)) {
            vc.error(ErrorCode.DegreeNumber);
            return false;
        }
        Integer num = Integer.parseInt(v);
        if (num != null && (num > 3000 || num < 1)) {
            vc.error(ErrorCode.NumberRangesFromOneToThreeThousand);
            return false;
        }
        return true;
    }

    private HttpServletResponse getHttpServletResponse(RequestContext request, String fileName) throws UnsupportedEncodingException {
        if (com.zxy.product.train.web.util.BrowserUtil.isMSBrowser(request.getRequest().getHeader("User-Agent"))) {
            fileName = URLEncoder.encode(fileName, "UTF-8");
        } else {
            fileName = new String(fileName.getBytes(StandardCharsets.UTF_8), StandardCharsets.ISO_8859_1);
        }
        HttpServletResponse response = request.getResponse();
        response.setContentType("application/octet-stream;charset=utf-8");
        response.setHeader("Content-Disposition", "attachment;filename=" + fileName);
        return response;
    }

    private String getAesEncryptParam(Optional<String> param) {
        if (param.isPresent()) {
            try {
                return Encrypt.aesEncrypt(param.get(), aesKey);
            } catch (Exception e) {
                e.printStackTrace();
                return null;
            }
        }
        return null;
    }

    private String getDecryptParam(Optional<String> param) {
        if (param.isPresent()) {
            try {
                return Decrypt(param.get(), aesKey);
            } catch (Exception e) {
                e.printStackTrace();
                return null;
            }
        }
        return null;
    }

    @Override
    public void setEnvironment(Environment environment) {
        aesKey = environment.getProperty("aes.key", "d8cg8gVakEq9Agup");
    }
}
