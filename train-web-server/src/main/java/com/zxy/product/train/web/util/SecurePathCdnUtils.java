package com.zxy.product.train.web.util;

import org.apache.commons.codec.digest.DigestUtils;

import java.util.UUID;

/**
 * <AUTHOR>
 * @date 2023/8/21 17:00
 */
public class SecurePathCdnUtils {

/*    @Value("${zxy.secure.file.secret4Cdn:zhixueyuncdnexp1234}")
    private static String secureFileSecret4Cdn;*/


    public static String generateSecurePathCdn(String uri) {
        if (org.jooq.tools.StringUtils.isEmpty(uri)) return "";
        String url = processSlash(uri);
        // 当前服务器时间
        String timestamp = String.valueOf(System.currentTimeMillis() / 1000);
        // uuid
        String rand = UUID.randomUUID().toString().replaceAll("-", "");
        // uid
        String uid = "0";
        String value = "/" + url + "-" + timestamp + "-" + rand + "-" + uid + "-" + "zhixueyuncdnexp1234";
        String otherMd5 = DigestUtils.md5Hex(value);
        return url + "?auth_key=" + timestamp + "-" + rand + "-" + uid + "-" + otherMd5;
    }
    public static String processSlash(String uri) {
        if (uri != null && uri.startsWith("/")) {
            return uri.substring(1);
        }
        return uri;
    }


}
