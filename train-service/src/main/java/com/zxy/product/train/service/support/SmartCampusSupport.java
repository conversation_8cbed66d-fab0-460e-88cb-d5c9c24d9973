package com.zxy.product.train.service.support;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.ImmutableMap;
import com.zxy.common.dao.Fields;
import com.zxy.common.dao.support.CommonDao;
import com.zxy.product.course.util.HttpClientUtil;
import com.zxy.product.train.api.ClassQuestionnaireTotalService;
import com.zxy.product.train.api.QuestionnaireQuestionTypeService;
import com.zxy.product.train.api.SignDetailService;
import com.zxy.product.train.api.SmartCampusService;
import com.zxy.product.train.api.ClassInfoService;
import com.zxy.product.train.entity.*;
import org.jooq.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.EnvironmentAware;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.crypto.Cipher;
import java.security.*;
import java.security.Key;
import java.security.spec.X509EncodedKeySpec;
import java.util.*;
import java.util.stream.Collectors;

import static com.zxy.product.train.jooq.Tables.*;
import static com.zxy.product.train.jooq.tables.ClassResource.CLASS_RESOURCE;
import static com.zxy.product.train.jooq.tables.ClassroomConfiguration.CLASSROOM_CONFIGURATION;
import static com.zxy.product.train.jooq.tables.ConfigurationValue.CONFIGURATION_VALUE;
import static org.apache.commons.codec.binary.Base64.decodeBase64;

@Service
public class SmartCampusSupport implements SmartCampusService, EnvironmentAware {

    private static Logger logger = LoggerFactory.getLogger(SmartCampusSupport.class);

    private final static String RSA_ALGORITHM = "RSA";
    private static final String SMART_CAMPUS_CLASS_SATISFACTION_UPDATE = "/api/bmpvoucher/open/student/satisfactionUpdate";
    private static final String SMART_CAMPUS_CLASS_ROOM_UPDATE = "api/bmpvoucher/TrainingClass/updateHistory";

    private String publicKey;
    private String domain;

    private CommonDao<Trainee> traineeDao;
    private CommonDao<Member> memberCommonDao;
    private CommonDao<Sign> signDao;
    private CommonDao<ClassEvaluate> classEvaluateCommonDao;
    private CommonDao<ConfigurationValue> configurationValueDao;
    private CommonDao<Project> projectDao;

    private SignDetailService signDetailService;
    private QuestionnaireQuestionTypeService questionnaireQuestionTypeService;
    private ClassQuestionnaireTotalService classQuestionnaireTotalService;
    private ClassInfoService classInfoService;




    @Autowired
    public void setTraineeDao(CommonDao<Trainee> traineeDao) {
        this.traineeDao = traineeDao;
    }

    @Autowired
    public void setSignDao(CommonDao<Sign> signDao) {
        this.signDao = signDao;
    }

    @Autowired
    public void setSignDetailService(SignDetailService signDetailService) {
        this.signDetailService = signDetailService;
    }

    @Autowired
    public void setMemberCommonDao(CommonDao<Member> memberCommonDao) {
        this.memberCommonDao = memberCommonDao;
    }

    @Autowired
    public void setQuestionnaireQuestionTypeService(QuestionnaireQuestionTypeService questionnaireQuestionTypeService) {
        this.questionnaireQuestionTypeService = questionnaireQuestionTypeService;
    }

    @Autowired
    public void setClassQuestionnaireTotalService(ClassQuestionnaireTotalService classQuestionnaireTotalService) {
        this.classQuestionnaireTotalService = classQuestionnaireTotalService;
    }

    @Autowired
    public void setClassEvaluateCommonDao(CommonDao<ClassEvaluate> classEvaluateCommonDao) {
        this.classEvaluateCommonDao = classEvaluateCommonDao;
    }

    @Autowired
    public void setConfigurationValueDao(CommonDao<ConfigurationValue> configurationValueDao) {
        this.configurationValueDao = configurationValueDao;
    }

    @Autowired
    public void setProjectDao(CommonDao<Project> projectDao) {
        this.projectDao = projectDao;
    }

    @Autowired
    public void setClassInfoService(ClassInfoService classInfoService) {
        this.classInfoService = classInfoService;
    }

    @Override
    public Map<String, String> register(String classId, String memberId, Long registerTime) {
        Optional<Member> member = getMember(memberId);
        if (member.isPresent()){
            String id = member.get().getId();
            Optional<Trainee> trainee = traineeDao.fetchOne(TRAINEE.CLASS_ID.eq(classId).and(TRAINEE.MEMBER_ID.eq(id)));
            if (!trainee.isPresent()) {
                return ImmutableMap.of("code", "30001", "msg", "班级或用户不存在", "result", "failed");
            }

            Trainee updateTrainee = trainee.get();
            updateTrainee.setRegisterTime(registerTime);
            updateTrainee.setRegister(Trainee.SHOW);
            traineeDao.update(updateTrainee);

            return ImmutableMap.of("code", "200", "msg", "成功", "result", "success");
        }
        return ImmutableMap.of("code", "30009", "msg", "未找到该学员", "result", "failed");
    }

    private Optional<Member> getMember(String memberId) {
        return memberCommonDao.fetchOne(MEMBER.ID.eq(memberId).or(MEMBER.NAME.eq(memberId)));
    }

    @Override
    public Map<String, String> studentAttendance(String classId, String memberId, Long attendanceTime) {
        Optional<Member> member = getMember(memberId);
        if (member.isPresent()){
            String id = member.get().getId();
            //根据classId查出该培训班下所有签到活动
            List<Sign> signs = signDao.fetch(SIGN.CLASS_ID.eq(classId));
            if (CollectionUtils.isEmpty(signs)) {
                return ImmutableMap.of("code", "30002", "msg", "培训班无签到活动", "result", "failed");
            }
            //根据签到类型和签到时间来match
            List<Sign> targetSigns = signs.stream().filter(x ->
                    x.getSignInType() != null
                            && x.getSignInType() == Sign.ATTENDANCE_MACHINE
                            && x.getStartTime() <= attendanceTime
                            && x.getEndTime() >= attendanceTime)
                    .collect(Collectors.toList());
            if (CollectionUtils.isEmpty(targetSigns)) {
                return ImmutableMap.of("code", "30003", "msg", "此时间点无签到活动", "result", "failed");
            }
            targetSigns.stream().forEach(x -> {
                signDetailService.qrSign(x.getId(), id, attendanceTime);
            });

            return ImmutableMap.of("code", "200", "msg", "成功", "result", "success");
        }
        return ImmutableMap.of("code", "30009", "msg", "未找到该学院", "result", "failed");
    }

    @Override
    public Map<String, Object> getJumpLink(Long currentTimestamp, String classId, String userName,boolean bearerNetwork) {
        StringBuilder sb = new StringBuilder();
        String data = sb.append(userName).append("_").append(classId).append("_").append(currentTimestamp).toString();
        logger.error("跳转智慧校园 param：{}", data);
        try {
            byte[] bytes = encryptPublicKey(data.getBytes(), publicKey);
            return ImmutableMap.of("data", Base64.getEncoder().encodeToString(bytes),"bearerNetwork", bearerNetwork);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    @Override
    public void pushOverallSatisfaction(List<String> ids) {

        logger.error("手动补偿培训班满意度接口");

        List<ClassEvaluate> classEvaluates = classEvaluateCommonDao.fetch(CLASS_EVALUATE.CLASS_ID.in(ids)
                                                                           .and(CLASS_EVALUATE.TYPE.eq(ClassEvaluate.TYPE_EVA_STU_NEW))
                                                                           .and(CLASS_EVALUATE.DELETE_FLAG.eq(0))
                                                                           .and(CLASS_EVALUATE.END_TIME.le(System.currentTimeMillis())));
        logger.error("手动补偿培训班满意度接口,符合条件数量 : {} ",classEvaluates.size());

        classEvaluates.stream().filter( x -> x.getClassId() != null && x.getResourceId() != null).forEach( x -> {
            String classId = x.getClassId();
            String resourceId = x.getResourceId();
            List<List<ClassQuestionnaireTotal>> courseList = getEvaluateStatistics(classId,resourceId
                    , Optional.ofNullable("1"));
            Map<String, Object> populationStatistics = classQuestionnaireTotalService.getPopulationStatistics(classId, resourceId, courseList, Optional.empty());

            ClassQuestionnaireTotal total =  (ClassQuestionnaireTotal)populationStatistics.get("total");
            String subitemSatisfaction = total.getSubitemSatisfaction();
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("classId",classId);
            jsonObject.put("satisfaction",Double.valueOf(subitemSatisfaction));
            logger.error("培训班满意度推送 param {}", jsonObject.toJSONString());
            String addResponse = HttpClientUtil.httpPost(domain + SMART_CAMPUS_CLASS_SATISFACTION_UPDATE, null, jsonObject.toJSONString());
            logger.error("培训班满意度推送 ，response :{}", addResponse);
        });
    }

    @Override
    public void fieldCompensation(List<String> ids) {
        logger.error("班级餐厅字段数据补偿");

        if (CollectionUtils.isEmpty(ids)){
            return;
        }
        // 餐厅字段查询
        Map<String, String> dinningRoomMap = queryRoomInfo(ids,
                                                           CONFIGURATION_VALUE,
                                                           CONFIGURATION_VALUE.ID,
                                                           CLASS_RESOURCE.DINING_ROOM,
                                                           CONFIGURATION_VALUE.NAME,
                                                           CONFIGURATION_VALUE.SORT,
                                                           CONFIGURATION_VALUE.TYPE_ID.eq(14));
        // 教室字段查询
        Map<String, String> classRoomMap = queryRoomInfo(ids,
                                                         CLASSROOM_CONFIGURATION,
                                                         CLASSROOM_CONFIGURATION.ID,
                                                         CLASS_RESOURCE.CLASSROOM,
                                                         CLASSROOM_CONFIGURATION.CLASSROOM,
                                                         CLASSROOM_CONFIGURATION.SORT,
                                                         null);

       JSONArray jsonArray = new JSONArray();
        ids.parallelStream().forEach(id -> {
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("classId", id);
            jsonObject.put("classRoom", classRoomMap.get(id));
            jsonObject.put("diningRoom", dinningRoomMap.get(id));
            jsonArray.add(jsonObject);
        });

        try {
            String paramStr = jsonArray.toString();
            logger.error("培训班班级餐厅字段数据补偿推送 param {}", paramStr);
            String addResponse = HttpClientUtil.httpPost(domain + SMART_CAMPUS_CLASS_ROOM_UPDATE, null, paramStr);
            logger.info("班级餐厅字段数据补偿 ，response :{}", addResponse);
        } catch (Exception e) {
            logger.error("推送失败，", e.getMessage(), e);
        }

    }

    @Override
    public void fieldCompensation2(List<String> ids) {
        logger.error("地点,班主任字段数据补偿");

        if (CollectionUtils.isEmpty(ids)){
            return;
        }
        Map<String, String> addressMap = queryAddressInfo(ids);// 培训地点

        Map<Object, List<Map<String, Object>>> teacherInfoMap = queryTeacherInfo(ids);// 班主任


        JSONArray jsonArray = new JSONArray();
        ids.parallelStream().forEach(id -> {
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("classId", id);
            jsonObject.put("placeForTraining", addressMap.get(id));
            jsonObject.put("teachers", teacherInfoMap.get(id));
            jsonArray.add(jsonObject);
        });

        try {
            String paramStr = jsonArray.toString();
            logger.error("培训地点,班主任字段数据补偿推送 param {}", paramStr);
            String addResponse = HttpClientUtil.httpPost(domain + SMART_CAMPUS_CLASS_ROOM_UPDATE, null, paramStr);
            logger.info("地点,班主任字段数据补偿 ，response :{}", addResponse);
        } catch (Exception e) {
            logger.error("推送失败，{}", e.getMessage());
        }

    }

    private <T> Map<String, String> queryRoomInfo(List<String> ids,
                                                  Table<?> fromTable,
                                                  Field<T> joinField,
                                                  Field<T> classResourceFiled,
                                                  Field<String> resultField,
                                                  Field<?> orderField,
                                                  Condition additionalCOndition) {

        SelectConditionStep<Record> query = configurationValueDao.execute(
                x -> x.select(Fields.start().add(CLASS_RESOURCE.CLASS_ID,
                                                resultField).end())
                 .from(fromTable)
                 .innerJoin(CLASS_RESOURCE).on(joinField.eq(classResourceFiled))
                 .where(CLASS_RESOURCE.CLASS_ID.in(ids))
                 .and(fromTable.field("f_delete_flag", Integer.class).eq(0))

        );

        if (additionalCOndition != null){
            query.and(additionalCOndition);
        }
        return query.orderBy(orderField.asc()).fetch().stream().collect(
                Collectors.groupingBy(x -> x.getValue(CLASS_RESOURCE.CLASS_ID),
                                      Collectors.mapping(
                                              x -> x.getValue(resultField),
                                              Collectors.joining("、")
                                      )));
    }
    private Map<Object, List<Map<String, Object>>> queryTeacherInfo(List<String> ids) {
        List<Map<String, Object>> list = configurationValueDao.execute(dsl -> dsl
                .select(CLASSSTAFF_CLASS.SORT, MEMBER.FULL_NAME, CLASSSTAFF_CLASS.CLASS_ID)
                .from(CLASSSTAFF_CLASS)
                .leftJoin(MEMBER).on(CLASSSTAFF_CLASS.MEMBER_ID.eq(MEMBER.ID))
                .where(CLASSSTAFF_CLASS.CLASS_ID.in(ids))
                .fetch(record -> {
                    Map<String, Object> res = new HashMap<>();
                    res.put("name", record.getValue(MEMBER.FULL_NAME));
                    res.put("sort", record.getValue(CLASSSTAFF_CLASS.SORT));
                    res.put("classId", record.getValue(CLASSSTAFF_CLASS.CLASS_ID));
                    return res;
                })
        );
        Map<Object, List<Map<String, Object>>> collect = list.stream().collect(Collectors.groupingBy(x -> x.get("classId"), Collectors.toList()));
        collect.forEach((k, v) -> {
                    v.forEach(x -> x.remove("classId"));
                });
        return collect;
    }

    private Map<String, String> queryAddressInfo(List<String> ids) {
        return configurationValueDao.execute(dslContext -> dslContext
                .select(CLASS_INFO.ID,CONFIGURATION_VALUE.NAME)
                .from(PROJECT)
                .join(CLASS_INFO).on(CLASS_INFO.PROJECT_ID.eq(PROJECT.ID))
                .leftJoin(CONFIGURATION_VALUE).on(PROJECT.ADDRESS.eq(CONFIGURATION_VALUE.ID))
                .where(CLASS_INFO.ID.in(ids))
                .fetchMap(CLASS_INFO.ID, CONFIGURATION_VALUE.NAME));
    }


    /**
     * 使用公钥对数据进行加密
     */
    private byte[] encryptPublicKey(byte[] binaryData, String publicKey) throws Exception {
        byte[] keyBytes = decodeBase64(publicKey);
        X509EncodedKeySpec keySpec = new X509EncodedKeySpec(keyBytes);

        // 获取RSA算法实例
        KeyFactory keyFactory = KeyFactory.getInstance(RSA_ALGORITHM);
        Key pubKey = keyFactory.generatePublic(keySpec);

        // 初始化加密器
        Cipher cipher = Cipher.getInstance(keyFactory.getAlgorithm());
        cipher.init(Cipher.ENCRYPT_MODE, pubKey);
        return cipher.doFinal(binaryData);
    }

    private List<List<ClassQuestionnaireTotal>> getEvaluateStatistics(String classId,
                                                                      String researchId, Optional<String> type){
        boolean flag=questionnaireQuestionTypeService.existPartyCadreClass(classId);

        if(flag){
            return classQuestionnaireTotalService.getPartyCadreCourseStatistics(classId, researchId,type);
        }else{
            return classQuestionnaireTotalService.getCourseStatistics(classId, researchId);
        }
    }


    @Override
    public Map<String, Object> finishClass(String classId) {
        Map<String, Object> result = new HashMap<>();

        try {
            // 1. 查询班级信息
            ClassInfo classInfo = classInfoService.get(classId);
            if (classInfo == null) {
                logger.warn("班级信息不存在，classId: {}", classId);
                result.put("success", false);
                result.put("message", "班级信息不存在");
                return result;
            }

            // 2. 检查是否已经手动结束
            Integer isManualFinish = classInfo.getIsManualFinish();
            if (isManualFinish != null && isManualFinish == 1) {
                logger.warn("班级已经手动结束，classId: {}, isManualFinish: {}", classId, isManualFinish);
                result.put("success", false);
                result.put("message", "班级已经结束，不允许再次结束！");
                return result;
            }

            // 3. 检查时间范围
            Long currentTime = System.currentTimeMillis();
            Long arriveDate = classInfo.getArriveDate();
            Long returnDate = classInfo.getReturnDate();

            if (arriveDate == null || returnDate == null) {
                logger.warn("班级时间信息不完整，classId: {}, arriveDate: {}, returnDate: {}",
                        classId, arriveDate, returnDate);
                result.put("success", false);
                result.put("message", "班级时间信息不完整");
                return result;
            }

            if (currentTime < arriveDate || currentTime > returnDate) {
                logger.info("当前时间不在培训班时间范围内，classId: {}, currentTime: {}, arriveDate: {}, returnDate: {}",
                        classId, currentTime, arriveDate, returnDate);
                result.put("success", false);
                result.put("message", "不在培训班的时间范围内，不允许同步！");
                return result;
            }

            // 4. 获取班级MIS编码
            String misCode = classInfo.getCode();
            if (misCode == null || misCode.trim().isEmpty()) {
                logger.warn("班级MIS编码为空，classId: {}", classId);
                result.put("success", false);
                result.put("message", "班级MIS编码为空，无法同步");
                return result;
            }

            // 5. 构造请求参数
            JSONObject requestJsonObj = new JSONObject();
            requestJsonObj.put("classId", misCode);
            String requestJson = requestJsonObj.toJSONString();

            // 6. 构造调用地址
            String url = domain + "/api/bmpvoucher/open/v1/class/inAdvanceByClassId";
            logger.info("开始班级结束同步到智慧教务，classId: {}, misCode: {}, url: {}, requestJson: {}",
                    classId, misCode, url, requestJson);

            // 7. 发送HTTP请求
            String response = HttpClientUtil.httpPost(url, null, requestJson);
            logger.info("智慧教务系统响应，classId: {}, response: {}", classId, response);

            // 8. 解析响应结果
            boolean syncSuccess = false;
            try {
                if (response != null && !response.trim().isEmpty()) {
                    JSONObject responseJson = JSONObject.parseObject(response);
                    Boolean success = responseJson.getBoolean("success");
                    syncSuccess = success != null && success;
                    logger.info("解析智慧教务系统响应，classId: {}, success: {}", classId, syncSuccess);
                } else {
                    logger.warn("智慧教务系统返回空响应，classId: {}", classId);
                }
            } catch (Exception e) {
                logger.error("解析智慧教务系统响应失败，classId: " + classId + ", response: " + response, e);
                syncSuccess = false;
            }

            if (syncSuccess) {
                logger.info("班级结束同步到智慧教务成功，classId: {}", classId);

                // 9. 更新项目的手动结束标识
                try {
                    String projectId = classInfo.getProjectId();
                    projectDao.execute(x -> x.update(PROJECT)
                            .set(PROJECT.IS_MANUAL_FINISH, 1)
                            .where(PROJECT.ID.eq(projectId))
                            .execute());
                    logger.info("更新项目手动结束标识成功，projectId: {}", projectId);
                } catch (Exception e) {
                    logger.error("更新项目手动结束标识失败，classId: " + classId, e);
                    // 注意：这里不影响主流程，只记录错误日志
                }

                result.put("success", true);
                result.put("message", "同步成功");
                return result;
            } else {
                logger.warn("智慧教务系统返回失败，classId: {}, response: {}", classId, response);
                result.put("success", false);
                result.put("message", "同步到智慧教务失败，请稍候重试或者联系管理员！");
                return result;
            }

        } catch (Exception e) {
            logger.error("班级结束同步到智慧教务异常，classId: " + classId, e);
            result.put("success", false);
            result.put("message", "同步到智慧教务失败，请稍候重试或者联系管理员！");
            return result;
        }
    }



    @Override
    public void setEnvironment(Environment environment) {
        publicKey = environment.getProperty("smart.campus.public.key", "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAipwtzs6sDtCn8VVVbr6t2HQVTeJS1Qi4mgq9VS8sk8e+QU+pOhgRrP/HQXiTcZFAb15u/kWxnMpis+M5MpQ/UWPpiW/2YW+HtxaZQNNx6c8toFUjRveSjN3yjh3kUXYI4+dLulKHRryZlV9bGwBKoxuHY7PXege4XWLK8pcG19MnKo3A5uvo6t6gRnvCNE2ej/bwbWL+zved8bDKX4+Jxh4JLPzksvejIfhn3ZfOj0AZcmiNkNp31WMXGT/UXIHGBSZLfsdwzbTmOW8oZwfdw2YUuAyKPoRGszQVnMsbrT3izLoj+Blc/AYs3wl0FYWl/dcl3J0yw7BzjrAiJODPrwIDAQAB");
        domain = environment.getProperty("smart.campus.domain", "http://*************:1667");
    }
}
