package com.zxy.product.train.service.util;

import com.zxy.product.train.entity.StudyTeam;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Date;
import java.util.Optional;

public class DateUtil {
    private static final Logger LOGGER = LoggerFactory.getLogger(DateUtil.class);

    public static final String YYYY_MM_DD = "yyyy-MM-dd";
    public static final String YYYY_MM_DD_HH_MM_SS = "yyyy-MM-dd HH:mm:ss";
    public static final String HH_MM = "HH:mm";
    public static final String DATE_PATTERN_YYYY = "yyyy";
    public static final String DATE_PATTERN_MM = "MM";
    public static final String DATE_PATTERN_YYYYMM = "yyyyMM";

    public static Long dateStringYYYYMMDD2Long(String date) {
        return dateString2Long(date, YYYY_MM_DD);
    }

    public static Long dateStringYYYYMMDDHHMMSS2Long(String date) {
        return dateString2Long(date, YYYY_MM_DD_HH_MM_SS);
    }

    public static Long dateString2Long(String date, String format) {
        SimpleDateFormat sdf = new SimpleDateFormat(format);
        Date d = null;
        try {
            d = sdf.parse(date);
        } catch (ParseException e) {
            LOGGER.error("string to date error", e);
        }

        return d.getTime();
    }

    public static Optional<Long> dateStringYYYYMMDDHHMMSS2OptionalLong(Optional<String> date) {
        return date.map(d -> {
            return dateStringYYYYMMDDHHMMSS2Long(d);
        });
    }
    public static Optional<Long> dateStringYYYYMMDD2OptionalLong(Optional<String> date) {
        return date.map(d -> {
            return dateStringYYYYMMDD2Long(d);
        });
    }

    /**
     * Long类型日期转String类型
     * @param dateLong
     * @param format
     * @return
     */
    public static String dateLongToString(Long dateLong,String format){
        SimpleDateFormat sdf = new SimpleDateFormat(format);
        Date date = new Date(dateLong);
        return sdf.format(date);
    }

    /**
     * 秒数转成时间时长显示
     * @param seconds
     * @return
     */
    public static String secondsToString(Integer seconds){
        String time = "0";
        Integer minute = 0; // 分
        Integer hour = 0; // 小时
        if (seconds > 60) {
            minute = seconds / 60;
            seconds = seconds % 60;
            if (minute > 60) {
                hour = minute / 60;
                minute = minute % 60;
            }
        }
        time = seconds.toString();
        if (minute > 0) {
            time = minute + ":" + time;
        }
        if (hour > 0) {
            time = hour + ":" + time;
        }
        return time;
    }

    /**
     * String类型的时间转化为Date类型的时间，格式为yyyy-MM-dd HH:mm:ss
     * @param date
     * @return
     */
    public static Date stringToYYYYMMDDHHMMSSDate(String date) {
        SimpleDateFormat sdf = new SimpleDateFormat(YYYY_MM_DD_HH_MM_SS);
        Date d = null;
        try {
            d = sdf.parse(date);
        } catch (ParseException e) {
            LOGGER.error("string to date error", e);
        }

        return d;
    }

    /**
     * 次日24天，就是第三天凌晨
     * @return
     */
    public static Long getTomorrow(){
        LocalDateTime now = LocalDateTime.now();
        // 获取次日00:00:00的LocalDateTime
        LocalDateTime nextDayMidnight = now.plusDays(2)
                .withHour(0)
                .withMinute(0)
                .withSecond(0)
                .withNano(0);
        // 转换为时间戳（毫秒）
        return nextDayMidnight.toInstant(ZoneOffset.ofHours(8)).toEpochMilli();
    }

    /**
     * String类型的时间转化为Date类型的时间，格式为yyyy-MM-dd
     * @param date
     * @return
     */
    public static Date stringToYYYYMMDDDate(String date) {
        SimpleDateFormat sdf = new SimpleDateFormat(YYYY_MM_DD);
        Date d = null;
        try {
            d = sdf.parse(date);
        } catch (ParseException e) {
            LOGGER.error("string to date error", e);
        }

        return d;
    }

    /**
     * 判断当前用户是不是管理员
     *
     * @param currentUserId                     当前用户
     * @param grantedMemberByUriAndOrganization 管理员名单
     * @return 是否是管理员
     */
    public static Integer judgeIsAdmin(String currentUserId, String[] grantedMemberByUriAndOrganization) {
        Integer adminFlag = StudyTeam.FLAG_NO;
        if (grantedMemberByUriAndOrganization != null && grantedMemberByUriAndOrganization.length > 0) {
            boolean b = Arrays.asList(grantedMemberByUriAndOrganization).parallelStream().anyMatch(e -> e.equals(currentUserId));
            if (b) {
                adminFlag = StudyTeam.FLAG_YES;
            }
        }
        return adminFlag;
    }

    public static long getYearStartTimestamp(){
        Calendar calendar = Calendar.getInstance(); // 创建一个Calendar对象

        int year = calendar.get(Calendar.YEAR); // 获取当前年份
        calendar.set(year, Calendar.JANUARY, 1, 0, 0, 0); // 设置为当前年的第一天零点零分零秒

        return calendar.getTimeInMillis();
    }

    public static long getYearEndTimestamp(){
        Calendar calendar = Calendar.getInstance(); // 创建一个Calendar对象

        int year = calendar.get(Calendar.YEAR); // 获取当前年份
        calendar.set(year, Calendar.DECEMBER, 31, 23, 59, 59); // 设置为当前年的第一天零点零分零秒

        return calendar.getTimeInMillis();
    }

    public static String format(Date date, String pattern) {
        Instant instant = date.toInstant();
        LocalDateTime localDateTime = instant.atZone(ZoneId.systemDefault()).toLocalDateTime();
        DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern(pattern);
        return localDateTime.format(dateTimeFormatter);
    }
}
