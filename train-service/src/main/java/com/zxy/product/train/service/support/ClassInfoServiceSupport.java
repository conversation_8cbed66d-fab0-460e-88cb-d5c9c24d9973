package com.zxy.product.train.service.support;

import com.alibaba.dubbo.common.utils.StringUtils;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.zxy.common.base.exception.UnprocessableException;
import com.zxy.common.base.helper.PagedResult;
import com.zxy.common.dao.Fields;
import com.zxy.common.dao.support.CommonDao;
import com.zxy.common.encrypt.SM4.SM4Utils;
import com.zxy.common.message.provider.MessageSender;
import com.zxy.product.course.content.CommonConstant;
import com.zxy.product.course.entity.QuestionnaireQuestion;
import com.zxy.product.train.api.*;
import com.zxy.product.train.content.MessageHeaderContent;
import com.zxy.product.train.content.MessageTypeContent;
import com.zxy.product.train.dto.ClassInfoDTO;
import com.zxy.product.train.entity.*;
import com.zxy.product.train.jooq.Tables;
import com.zxy.product.train.jooq.tables.pojos.ClassInfoEntity;
import com.zxy.product.train.service.util.DateUtil;
import com.zxy.product.train.service.util.DesensitizationUtil;
import org.jooq.*;
import org.jooq.impl.DSL;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalTime;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.zxy.product.train.entity.ClassInfo.*;
import static com.zxy.product.train.entity.SignDetail.AUDITED;
import static com.zxy.product.train.entity.Trainee.AUDIT_AGREE;
import static com.zxy.product.train.entity.Trainee.TRAINEE_TYPE_FORMAL;
import static com.zxy.product.train.jooq.Tables.*;
import static com.zxy.product.train.jooq.tables.ClassDetail.CLASS_DETAIL;
import static com.zxy.product.train.jooq.tables.ClassInfo.CLASS_INFO;
import static com.zxy.product.train.jooq.tables.ClassOfflineCourse.CLASS_OFFLINE_COURSE;
import static com.zxy.product.train.jooq.tables.ClassResource.CLASS_RESOURCE;
import static com.zxy.product.train.jooq.tables.ClassSignupInfo.CLASS_SIGNUP_INFO;
import static com.zxy.product.train.jooq.tables.ClassroomConfiguration.CLASSROOM_CONFIGURATION;
import static com.zxy.product.train.jooq.tables.ClassstaffClass.CLASSSTAFF_CLASS;
import static com.zxy.product.train.jooq.tables.ConfigurationValue.CONFIGURATION_VALUE;
import static com.zxy.product.train.jooq.tables.GrantDetail.GRANT_DETAIL;
import static com.zxy.product.train.jooq.tables.Job.JOB;
import static com.zxy.product.train.jooq.tables.Member.MEMBER;
import static com.zxy.product.train.jooq.tables.Organization.ORGANIZATION;
import static com.zxy.product.train.jooq.tables.OrganizationDetail.ORGANIZATION_DETAIL;
import static com.zxy.product.train.jooq.tables.Position.POSITION;
import static com.zxy.product.train.jooq.tables.Project.PROJECT;
import static com.zxy.product.train.jooq.tables.QuestionnaireQuestionType.QUESTIONNAIRE_QUESTION_TYPE;
import static com.zxy.product.train.jooq.tables.ResearchAnswerRecord.RESEARCH_ANSWER_RECORD;
import static com.zxy.product.train.jooq.tables.ResearchQuestionary.RESEARCH_QUESTIONARY;
import static com.zxy.product.train.jooq.tables.ResearchRecord.RESEARCH_RECORD;
import static com.zxy.product.train.jooq.tables.Settlement.SETTLEMENT;
import static com.zxy.product.train.jooq.tables.Sign.SIGN;
import static com.zxy.product.train.jooq.tables.Trainee.TRAINEE;

/**
 * 班级信息.
 *
 * <AUTHOR>
 * @date 2017/2/8
 */
@Service
public class ClassInfoServiceSupport implements ClassInfoService {

  public static final Logger logger = LoggerFactory.getLogger(ClassInfoServiceSupport.class);
  private CommonDao<ClassInfo> classDao;
  private CommonDao<MemberConfig> memberConfigCommonDao;
  private CommonDao<Settlement> settlementCommonDao;
  private CommonDao<GroupConfigurationValue> groupConfigurationValueDao;
  private CommonDao<ClassResource> classResourceDao;

  private CommonDao<ClassDetail> detailDao;
  @Resource
  private CommonDao<DeleteDataTrain> deleteDataTrainCommonDao;


  private MemberService memberService;

  private CommonDao<Position> positionDao;

  private ClassResourceService resourceService;

  private ClassDetailService detailService;

  private ClassstaffClassService classstaffClassService;

  private ClassRequiredService classRequiredService;

  private MessageSender sender;
  private CommonDao<ClassEvaluate> evaluateDao;
  private CommonDao<Bus> busDao;
  private CommonDao<Member> memberDao;
  private CommonDao<Organization> orgDao;
  private CommonDao<ConfigurationValue> configurationValueDao;
  private CommonDao<ClassroomConfiguration> classroomConfigurationDao;
  private CommonDao<Trainee> traineeDao;
  private CommonDao<Sign> signDao;
  private QuestionnaireQuestionTypeService questionnaireQuestionTypeService;
  private CommonDao<ClassOfflineCourse> classOfflineCourseCommonDao;
  private TrainChatGroupService trainChatGroupService;
  private CommonDao<com.zxy.product.train.entity.Trainee> traineeCommonDao;
  private CommonDao<StudyReportTrain.StudyReportTrain_2025> studyReportTrainDao;


  @Autowired
  public void setClassOfflineCourseCommonDao(CommonDao<ClassOfflineCourse> classOfflineCourseCommonDao) {
    this.classOfflineCourseCommonDao = classOfflineCourseCommonDao;
  }

  @Autowired
  public void setGroupConfigurationValueDao(
      CommonDao<GroupConfigurationValue> groupConfigurationValueDao) {
    this.groupConfigurationValueDao = groupConfigurationValueDao;
  }

  @Autowired
  public void setSignDao(CommonDao<Sign> signDao) {
    this.signDao = signDao;
  }

  private CommonDao<ClassOfflineCourse> classOfflineCourseDao;

  @Autowired
  public void setPositionDao(CommonDao<Position> positionDao) {
    this.positionDao = positionDao;
  }

  @Autowired
  public void setClassroomConfigurationDao(
      CommonDao<ClassroomConfiguration> classroomConfigurationDao) {
    this.classroomConfigurationDao = classroomConfigurationDao;
  }


  @Autowired
  public void setConfigurationValueDao(CommonDao<ConfigurationValue> configurationValueDao) {
    this.configurationValueDao = configurationValueDao;
  }

  @Autowired
  public void setClassstaffClassService(ClassstaffClassService classstaffClassService) {
    this.classstaffClassService = classstaffClassService;
  }

  @Autowired
  public void setMemberConfigCommonDao(CommonDao<MemberConfig> memberConfigCommonDao) {
    this.memberConfigCommonDao = memberConfigCommonDao;
  }

  @Autowired
  public void setSettlementCommonDao(CommonDao<Settlement> settlementCommonDao) {
    this.settlementCommonDao = settlementCommonDao;
  }

  @Autowired
  public void setDetailDao(CommonDao<ClassDetail> detailDao) {
    this.detailDao = detailDao;
  }

  @Autowired
  public void setMemberService(MemberService memberService) {
    this.memberService = memberService;
  }

  @Autowired
  public void setResourceService(ClassResourceService resourceService) {
    this.resourceService = resourceService;
  }

  @Autowired
  public void setDetailService(ClassDetailService detailService) {
    this.detailService = detailService;
  }

  @Autowired
  public void setClassDao(CommonDao<ClassInfo> classDao) {
    this.classDao = classDao;
  }

  @Autowired
  public void setClassResourceDao(CommonDao<ClassResource> classResourceDao) {
    this.classResourceDao = classResourceDao;
  }

  @Autowired
  public void setSender(MessageSender sender) {
    this.sender = sender;
  }

  @Autowired
  public void setEvaluateDao(CommonDao<ClassEvaluate> evaluateDao) {
    this.evaluateDao = evaluateDao;
  }


  @Autowired
  public void setBusDao(CommonDao<Bus> busDao) {
    this.busDao = busDao;
  }

  @Autowired
  public void setMemberDao(CommonDao<Member> memberDao) {
    this.memberDao = memberDao;
  }

  @Autowired
  public void setOrgDao(CommonDao<Organization> orgDao) {
    this.orgDao = orgDao;
  }

  @Autowired
  public void setTraineeDao(CommonDao<Trainee> traineeDao) {
    this.traineeDao = traineeDao;
  }

  @Autowired
  public void setClassOfflineCourseDao(CommonDao<ClassOfflineCourse> classOfflineCourseDao) {
    this.classOfflineCourseDao = classOfflineCourseDao;
  }

  @Autowired
  public void setClassRequiredService(ClassRequiredService classRequiredService) {
	this.classRequiredService = classRequiredService;
}

  @Autowired
  public void setQuestionnaireQuestionTypeService(QuestionnaireQuestionTypeService questionnaireQuestionTypeService) {
    this.questionnaireQuestionTypeService = questionnaireQuestionTypeService;
  }

  @Autowired
  public void setTrainChatGroupService(TrainChatGroupService trainChatGroupService) {
    this.trainChatGroupService = trainChatGroupService;
  }

  @Autowired
  public void setTraineeCommonDao(CommonDao<com.zxy.product.train.entity.Trainee> traineeCommonDao) {
    this.traineeCommonDao = traineeCommonDao;
  }

  @Autowired
  public void setStudyReportTrainDao(CommonDao<StudyReportTrain.StudyReportTrain_2025> studyReportTrainDao) {
    this.studyReportTrainDao = studyReportTrainDao;
  }



  /**
   * 按条件查询班级信息
   *
   * @param page
   * @param pageSize
   * @param name
   * @param MIScode
   * @param orgName
   * @param status
   * @param implementation_year
   * @param implementation_month
   * @return
   */
  @Override
  public PagedResult<ClassInfo> find(int page, int pageSize, Optional<String> name,
      Optional<String> MIScode,
      Optional<String> orgName, Optional<Integer> status,
      Optional<Integer> isOutside, Optional<Long> reportBegin,
      Optional<Long> reportEnd,
      Optional<Long> returnBegin, Optional<Long> returnEnd, Optional<Integer> implementation_year,
      Optional<Integer> implementation_month, Optional<Integer> flag,
      List<String> organizationIds) {
    // 为需求方联系人给人员表起别名
//        Field<String> projectName = PROJECT.NAME.as("projectName");
//        Field<String> organizationName = ORGANIZATION.NAME.as("organizationName");
//        Optional<Integer> implementationMonth = Optional.empty();
//        Optional<Integer> implementationYear = Optional.empty();
//        if(implementation_month.isPresent()) {
//            implementationMonth = implementation_month.get() == 0 ? Optional.empty() : implementation_month;
//        }
//        if(implementation_year.isPresent()) {
//            implementationYear = implementation_year.get() == 0 ? Optional.empty() : implementation_year;
//        }

    // 构建查询语句
//        Long date = System.currentTimeMillis();
    //存储人员id
    List<String> memberIds = new ArrayList<>();
    List<String> orgIds = new ArrayList<>();
    List<ClassInfo> list = classDao.execute(d -> {
      Table<Record1<String>> basic = (d.select(CLASS_INFO.ID)
          .from(CLASS_INFO)
          .innerJoin(PROJECT).on(PROJECT.ID.eq(CLASS_INFO.PROJECT_ID))
          .where(CLASS_INFO.DELETE_FLAG.eq(ClassInfo.DELETE_FLASE))
          .and(name.map(PROJECT.NAME::contains).orElse(DSL.trueCondition()))
          .and(MIScode.map(PROJECT.CODE::contains).orElse(DSL.trueCondition()))
          .and(implementation_month.map(CLASS_INFO.IMPLEMENTATION_MONTH::eq)
              .orElse(DSL.trueCondition()))
          .and(implementation_year.map(CLASS_INFO.IMPLEMENTATION_YEAR::eq)
              .orElse(DSL.trueCondition()))
          .and(reportBegin.map(CLASS_INFO.ARRIVE_DATE::ge).orElse(DSL.trueCondition()))
          .and(reportEnd.map(CLASS_INFO.ARRIVE_DATE::le).orElse(DSL.trueCondition()))
          .and(returnBegin.map(CLASS_INFO.RETURN_DATE::ge).orElse(DSL.trueCondition()))
          .and(returnEnd.map(CLASS_INFO.RETURN_DATE::le).orElse(DSL.trueCondition()))
          .and(PROJECT.ORGANIZATION_ID.in(organizationIds))
          .and(CLASS_INFO.DELETE_FLAG.eq(ClassInfo.DELETE_FLASE))
          .and(PROJECT.STATUS.eq(3))
          .and(status.map(CLASS_INFO.STATUS::eq).orElse(DSL.trueCondition()))
          .and(isOutside.map(CLASS_INFO.IS_OUTSIDE::eq).orElse(DSL.trueCondition()))
          .orderBy(CLASS_INFO.ARRIVE_DATE.desc(), CLASS_INFO.CREATE_TIME.desc())
          .limit((page - 1) * pageSize, pageSize)
      ).asTable("b");

      return d.select(Fields.start()
          .add(CLASS_INFO.ID, CLASS_INFO.PROJECT_ID, CLASS_INFO.STATUS, CLASS_INFO.ARRIVE_DATE)
          .add(CLASS_INFO.IMPLEMENTATION_MONTH, CLASS_INFO.IMPLEMENTATION_YEAR,
              CLASS_INFO.RETURN_DATE, CLASS_INFO.IS_OUTSIDE)
          .add(PROJECT.CODE, PROJECT.NAME, PROJECT.CONTACT_PHONE, PROJECT.ORGANIZATION_ID,
              PROJECT.CONTACT_MEMBER_ID)
          .end())
          .from(CLASS_INFO)
          .innerJoin(basic).on(basic.field(CLASS_INFO.ID).eq(CLASS_INFO.ID))
          .innerJoin(PROJECT).on(PROJECT.ID.eq(CLASS_INFO.PROJECT_ID))
          .fetch(r -> {
            ClassInfo cf = r.into(ClassInfo.class);
            cf.setClassName(r.getValue(PROJECT.NAME));
            cf.setCode(r.getValue(PROJECT.CODE));
            String phoneNumber = r.getValue(PROJECT.CONTACT_PHONE);
            if (phoneNumber != null && !"".equals(phoneNumber)) {
              //脱敏
              cf.setContactPhone(DesensitizationUtil.desensitizeMobile(Optional.of(phoneNumber)));// 需求方电话
            }
            cf.setOrganizationId(r.getValue(PROJECT.ORGANIZATION_ID));
            cf.setContactPeople(r.getValue(PROJECT.CONTACT_MEMBER_ID));
            cf.setStatus(r.getValue(CLASS_INFO.STATUS));//班级状态
            cf.setIsOutside(r.getValue(CLASS_INFO.IS_OUTSIDE));//培训方式 0：内部培训 1：院外培训 2：在线学习
            memberIds.add(r.getValue(PROJECT.CONTACT_MEMBER_ID));
            orgIds.add(r.getValue(PROJECT.ORGANIZATION_ID));
            return cf;
          });
    });

    Map<String, Member> map = memberDao.execute(x -> x.select(MEMBER.ID, MEMBER.FULL_NAME)
        .from(MEMBER).where(MEMBER.ID.in(memberIds))).fetch(r -> r.into(Member.class)).stream()
        .collect(Collectors.toMap(Member::getId, o -> o));
    Map<String, Organization> orgMap = orgDao.execute(
        x -> x.select(ORGANIZATION.ID, ORGANIZATION.NAME).from(ORGANIZATION)
            .where(ORGANIZATION.ID.in(orgIds)))
        .fetch(r -> r.into(Organization.class)).stream()
        .collect(Collectors.toMap(Organization::getId, r -> r));

    Integer count = classDao.execute(d -> d.select(CLASS_INFO.ID.count())
        .from(CLASS_INFO)
        .innerJoin(PROJECT).on(PROJECT.ID.eq(CLASS_INFO.PROJECT_ID))
        .where(CLASS_INFO.DELETE_FLAG.eq(ClassInfo.DELETE_FLASE))
        .and(name.map(PROJECT.NAME::contains).orElse(DSL.trueCondition()))
        .and(MIScode.map(PROJECT.CODE::contains).orElse(DSL.trueCondition()))
        .and(implementation_month.map(CLASS_INFO.IMPLEMENTATION_MONTH::eq)
            .orElse(DSL.trueCondition()))
        .and(
            implementation_year.map(CLASS_INFO.IMPLEMENTATION_YEAR::eq).orElse(DSL.trueCondition()))
        .and(reportBegin.map(CLASS_INFO.ARRIVE_DATE::ge).orElse(DSL.trueCondition()))
        .and(reportEnd.map(CLASS_INFO.ARRIVE_DATE::le).orElse(DSL.trueCondition()))
        .and(returnBegin.map(CLASS_INFO.RETURN_DATE::ge).orElse(DSL.trueCondition()))
        .and(returnEnd.map(CLASS_INFO.RETURN_DATE::le).orElse(DSL.trueCondition()))
        .and(PROJECT.ORGANIZATION_ID.in(organizationIds))
        .and(CLASS_INFO.DELETE_FLAG.eq(ClassInfo.DELETE_FLASE))
        .and(PROJECT.STATUS.eq(3))
        .and(status.map(CLASS_INFO.STATUS::eq).orElse(DSL.trueCondition())))
        .and(isOutside.map(CLASS_INFO.IS_OUTSIDE::eq).orElse(DSL.trueCondition()))
        .fetchOne(CLASS_INFO.ID.count());

    return PagedResult.create(count, list.stream().map(m -> {
      if (map.get(m.getContactPeople()) != null) {
        m.setContactPeople(map.get(m.getContactPeople()).getFullName());
      }
      if (orgMap.get(m.getOrganizationId()) != null) {
        m.setOrganization(orgMap.get(m.getOrganizationId()).getName());
      }
      return m;
    }).collect(Collectors.toList()));

  }

  @Override
  public PagedResult<ClassInfo> findClass(int page, int pageSize, int config, Optional<String> name,
      Optional<String> classType,
      Optional<Integer> month, Optional<Integer> course, Optional<Integer> year,
      Optional<Long> start, Optional<Long> end,
      Optional<String> organization, List<String> organizationIds) {
    Field<String> projectName = PROJECT.NAME.as("projectName");
    Field<String> projectId = PROJECT.ID.as("projectId");
    Field<String> organizationName = ORGANIZATION.NAME.as("organizationName");
    Optional<String> typeId =
        classType.isPresent() && !("999").equals(classType.get()) ? classType : Optional.empty();
    // 是否关联在线课程
    Optional<Integer> courseType =
        course.isPresent() && !(Integer.valueOf("999")).equals(course.get()) ? course
            : Optional.empty();
    String sql = "1=1";
    if (start.isPresent() && end.isPresent()) {
      sql = "(train.t_class_info.f_arrive_date <= " + end.get()
          + " AND train.t_class_info.f_arrive_date >= " + start.get() + ") " +
          "OR (train.t_class_info.f_return_date <= " + end.get()
          + " AND train.t_class_info.f_return_date >=" + start.get() + ") " ;
    }
    String finalSql = sql;
    SelectOnConditionStep<Record> step = classDao.execute(x -> x
        .selectDistinct(Fields.start()
            .add(CLASS_INFO.ID, projectId,
                PROJECT.CODE, projectName, organizationName, CLASS_INFO.ARRIVE_DATE,
                CLASS_INFO.RETURN_DATE,
                CONFIGURATION_VALUE.NAME,
                PROJECT.ORGANIZATION_ID)
            .end())
        .from(PROJECT).leftJoin(CLASS_INFO).on(CLASS_INFO.PROJECT_ID.eq(PROJECT.ID))
        .leftJoin(CONFIGURATION_VALUE).on(PROJECT.TYPE_ID.eq(CONFIGURATION_VALUE.ID))
        .leftJoin(ORGANIZATION).on(PROJECT.ORGANIZATION_ID.eq(ORGANIZATION.ID)));
    // 构建组合条件
    List<Integer> projectStatus = Lists.newArrayList(Project.STATUS_RESERVE,
        Project.STATUS_APPROVAL,Project.STATUS_AGREE);
    Stream<Optional<Condition>> conditions = Stream.of(name.map(PROJECT.NAME::contains),
        Optional.of(organizationIds).map(PROJECT.ORGANIZATION_ID::in),
        typeId.map(PROJECT.TYPE_ID::eq),
        Optional.of(ClassInfo.DELETE_FLASE).map(PROJECT.DELETE_FLAG::eq),
        month.map(PROJECT.MONTH::eq),
        year.map(PROJECT.YEAR::eq),
        organization.map(ORGANIZATION.PATH::contains),
        Optional.of(projectStatus).map(x -> PROJECT.STATUS.in(x))
        );
    // 合并条件
    Condition c = conditions.filter(Optional::isPresent).map(Optional::get)
        .reduce((acc, item) -> acc.and(item))
        .orElse(DSL.trueCondition());
    List<String> projectIds = classRequiredService.findAllProjects().stream().map(ClassRequired::getProjectId).collect(Collectors.toList());
    Condition typeCondition = courseType.map(tt -> tt == 1 ? PROJECT.ID.in(projectIds) : PROJECT.ID.notIn(projectIds)).orElse(DSL.trueCondition());
    Integer count = classDao.execute(e -> e.fetchCount(step.where(c).and(typeCondition).and(finalSql)));
//        c.and(GRANT_DETAIL.MEMBER_ID.eq(memberId)).and(GRANT_DETAIL.URI.eq(ClassInfoService.URI))
    List<ClassInfo> list = step.where(c).and(typeCondition).and(finalSql)
        .orderBy(CLASS_INFO.ARRIVE_DATE.desc(), CLASS_INFO.CREATE_TIME.desc())
        .limit((page - 1) * pageSize, pageSize).fetch(r -> {
          ClassInfo cf = new ClassInfo();
          cf.setId(r.getValue(CLASS_INFO.ID));
          cf.setProjectId(r.getValue(projectId));
          cf.setArriveDate(r.getValue(CLASS_INFO.ARRIVE_DATE));
          cf.setReturnDate(r.getValue(CLASS_INFO.RETURN_DATE));
          cf.setClassName(r.getValue(projectName));
          cf.setCode(r.getValue(PROJECT.CODE));
          cf.setType(r.getValue(CONFIGURATION_VALUE.NAME));
          cf.setOrganization(r.getValue(organizationName)); // 需求方
          cf.setOrganizationId(r.getValue(PROJECT.ORGANIZATION_ID));
          return cf;
        });
    return PagedResult.create(count, list);
  }

  @Override
  public ClassInfo get(String id) {
    // 为需求方联系人给人员表起别名
    com.zxy.product.train.jooq.tables.Member member3 = MEMBER.as("member3");
    com.zxy.product.train.jooq.tables.ConfigurationValue configurationValue = CONFIGURATION_VALUE
        .as("configurationValue");
    com.zxy.product.train.jooq.tables.ConfigurationValue configurationValue1 = CONFIGURATION_VALUE
        .as("configurationValue1");
    Field<String> projectName = PROJECT.NAME.as("projectName");
    Field<String> organizationName = ORGANIZATION.NAME.as("organizationName");
    Field<String> contactName = member3.FULL_NAME.as("contactName");
    // 拼写Sql
    SelectConditionStep<Record> step = classDao.execute(x -> x
        .selectDistinct(Fields.start()
            .add(CLASS_INFO.ID, projectName, CLASS_INFO.CLASS_TEACHER,
                CLASS_INFO.CLASS_TEACHER_PHONE, CLASS_INFO.ARRIVE_DATE, CLASS_INFO.RETURN_DATE,
                PROJECT.CODE,
                organizationName, contactName, MEMBER.FULL_NAME, MEMBER.PHONE_NUMBER,
                CLASS_INFO.STATUS,
                CLASS_INFO.IMPLEMENTATION_MONTH, CLASS_INFO.IMPLEMENTATION_YEAR,
                PROJECT.CONTACT_EMAIL,
                MEMBER.EMAIL, CLASS_INFO.COURSE_SALARY, PROJECT.CONTACT_PHONE, PROJECT.AMOUNT,
                configurationValue.NAME, configurationValue1.NAME, CLASS_INFO.NOTICE,
                PROJECT.OBJECT, PROJECT.DAYS, PROJECT.ORGANIZATION_ID,PROJECT.IS_PARTY_CADRE,PROJECT.IS_MANUAL_FINISH,
                CLASS_INFO.GROUP_ID, CONFIGURATION_VALUE.NAME,
                CLASS_INFO.PROJECT_ID, CLASS_INFO.CREATE_TIME, CLASS_INFO.CLASS_LEVEL,
                    CLASS_INFO.CREATE_MEMBER,CLASS_INFO.TRAINEE_NUM,CLASS_INFO.DELETE_FLAG,CLASS_INFO.IS_OUTSIDE)
            .end())
        .from(CLASS_INFO)
        .innerJoin(PROJECT).on(CLASS_INFO.PROJECT_ID.eq(PROJECT.ID))
        .leftJoin(ORGANIZATION)
        .on(PROJECT.ORGANIZATION_ID.eq(ORGANIZATION.ID))
        .leftJoin(member3)
        .on(member3.ID.eq(PROJECT.CONTACT_MEMBER_ID))
        .leftJoin(MEMBER)
        .on(MEMBER.ID.eq(CLASS_INFO.CLASS_TEACHER))
        .leftJoin(configurationValue)
        .on(configurationValue.ID.eq(PROJECT.TYPE_ID))
        .leftJoin(configurationValue1)
        .on(configurationValue1.ID.eq(PROJECT.COST))
        .leftJoin(CONFIGURATION_VALUE)
        .on(CONFIGURATION_VALUE.ID.eq(PROJECT.ADDRESS)).where(CLASS_INFO.ID.eq(id)));
    // 输出页面显示数据
    ClassInfo classInfo = step.fetchOne(r -> {
      ClassInfo cf = r.into(ClassInfo.class);
      cf.setImplementationMonth(r.getValue(CLASS_INFO.IMPLEMENTATION_MONTH));
      cf.setImplementationYear(r.getValue(CLASS_INFO.IMPLEMENTATION_YEAR));
      cf.setReturnDate(r.getValue(CLASS_INFO.RETURN_DATE));
      cf.setArriveDate(r.getValue(CLASS_INFO.ARRIVE_DATE));
      cf.setClassName(r.getValue(projectName));
      cf.setCourseSalary(r.getValue(CLASS_INFO.COURSE_SALARY));
      cf.setCode(r.getValue(PROJECT.CODE));
      cf.setOrganization(r.getValue(organizationName)); // 需求方
      cf.setOrganizationId(r.getValue(PROJECT.ORGANIZATION_ID));
      cf.setNotice(r.getValue(CLASS_INFO.NOTICE));//是否发布
      Member member = new Member();
      member.setFullName(r.getValue(MEMBER.FULL_NAME));
      cf.setMember(member);
      cf.setClassTeacher(r.getValue(CLASS_INFO.CLASS_TEACHER));//班主任id
      String teacherPhone = SM4Utils.decryptDataCBC(r.getValue(MEMBER.PHONE_NUMBER));
      if (r.getValue(CLASS_INFO.CLASS_TEACHER_PHONE) != null) {
        teacherPhone = r.getValue(CLASS_INFO.CLASS_TEACHER_PHONE);
      }
      cf.setTeacherPhone(teacherPhone); // 班主任电话
      cf.setContachEmail(r.getValue(PROJECT.CONTACT_EMAIL)); // 需求方邮箱
      cf.setTeacherEmail(SM4Utils.decryptDataCBC(r.getValue(MEMBER.EMAIL)));// 班主任邮箱
      cf.setContactPeople(r.getValue(contactName));// 需求联系人
      cf.setContactPhone(r.getValue(PROJECT.CONTACT_PHONE));// 需求方电话
//            cf.setRestRoom(r.getValue(CLASS_RESOURCE.REST_ROOM));// 客房
//            cf.setDiningRoom(r.getValue(CLASS_RESOURCE.DINING_ROOM));// 餐厅
//            cf.setClassRoom(r.getValue(CLASS_RESOURCE.CLASSROOM));// 教室
      cf.setTrainObject(r.getValue(PROJECT.OBJECT)); // 培训对象
      cf.setStatus(r.getValue(CLASS_INFO.STATUS)); // 班级状态
      cf.setDays(r.getValue(PROJECT.DAYS));// 计划天数
      cf.setAmount(r.getValue(PROJECT.AMOUNT));
      cf.setType(r.getValue(configurationValue.NAME));
      cf.setCost(r.getValue(configurationValue1.NAME));
      cf.setGroupId(r.getValue(CLASS_INFO.GROUP_ID));
      cf.setAddress(r.getValue(CONFIGURATION_VALUE.NAME));
//            cf.setClassRoomName(r.getValue(CLASSROOM_CONFIGURATION.CLASSROOM));// 教室
      cf.setProjectId(r.getValue(CLASS_INFO.PROJECT_ID));
      cf.setCreateMember(r.getValue(CLASS_INFO.CREATE_MEMBER));
      cf.setTraineeNum(r.getValue(CLASS_INFO.TRAINEE_NUM));
      cf.setDeleteFlag(r.getValue(CLASS_INFO.DELETE_FLAG));
      cf.setIsPartyCadre(r.getValue(PROJECT.IS_PARTY_CADRE));//是否是党干部培训班
      cf.setIsManualFinish(r.getValue(PROJECT.IS_MANUAL_FINISH));//是否手动结束：0-否，1-是
      cf.setIsOutside(r.getValue(CLASS_INFO.IS_OUTSIDE));//培训方式 0：内部培训，1：院外培训 2：在线学习
      return cf;
    });

    List<ConfigurationValue> restaurantsConfig = configurationValueDao
        .execute(x -> x.select(Fields.start().add(CONFIGURATION_VALUE).end())
            .from(CONFIGURATION_VALUE)
            .innerJoin(CLASS_RESOURCE).on(CLASS_RESOURCE.DINING_ROOM.eq(CONFIGURATION_VALUE.ID))
            .where(CLASS_RESOURCE.CLASS_ID.eq(id))
            .and(CONFIGURATION_VALUE.DELETE_FLAG.eq(0))
            .and(CONFIGURATION_VALUE.TYPE_ID.eq(14))
            .orderBy(CONFIGURATION_VALUE.SORT.asc())
        ).fetch(b -> {
          ConfigurationValue taskReviewer = b.into(ConfigurationValue.class);
          taskReviewer.setId(b.getValue(CONFIGURATION_VALUE.ID));
          taskReviewer.setName(b.getValue(CONFIGURATION_VALUE.NAME));
          return taskReviewer;
        });

    List<ConfigurationValue> guestroomsConfig = configurationValueDao
        .execute(x -> x.select(Fields.start().add(CONFIGURATION_VALUE).end())
            .from(CONFIGURATION_VALUE)
            .innerJoin(CLASS_RESOURCE).on(CLASS_RESOURCE.REST_ROOM.eq(CONFIGURATION_VALUE.ID))
            .where(CLASS_RESOURCE.CLASS_ID.eq(id))
            .and(CONFIGURATION_VALUE.DELETE_FLAG.eq(0))
            .and(CONFIGURATION_VALUE.TYPE_ID.eq(15))
            .orderBy(CONFIGURATION_VALUE.SORT.asc())
        ).fetch(b -> {
          ConfigurationValue taskReviewer = b.into(ConfigurationValue.class);
          taskReviewer.setId(b.getValue(CONFIGURATION_VALUE.ID));
          taskReviewer.setName(b.getValue(CONFIGURATION_VALUE.NAME));
          return taskReviewer;
        });

    List<ClassroomConfiguration> classroomsConfig = classroomConfigurationDao
        .execute(x -> x.select(Fields.start().add(CLASSROOM_CONFIGURATION).end())
            .from(CLASSROOM_CONFIGURATION)
            .innerJoin(CLASS_RESOURCE).on(CLASS_RESOURCE.CLASSROOM.eq(CLASSROOM_CONFIGURATION.ID))
            .where(CLASS_RESOURCE.CLASS_ID.eq(id))
            .and(CLASSROOM_CONFIGURATION.DELETE_FLAG.eq(0))
            .orderBy(CLASSROOM_CONFIGURATION.SORT.asc())
        ).fetch(b -> {
          ClassroomConfiguration taskReviewer = b.into(ClassroomConfiguration.class);
          taskReviewer.setId(b.getValue(CLASSROOM_CONFIGURATION.ID));
          taskReviewer.setClassroom(b.getValue(CLASSROOM_CONFIGURATION.CLASSROOM));
          return taskReviewer;
        });
    List<ConfigurationValue> resConfig =
        restaurantsConfig == null ? new ArrayList<ConfigurationValue>() : restaurantsConfig;
    List<ConfigurationValue> guestConfig =
        guestroomsConfig == null ? new ArrayList<>() : guestroomsConfig;


      // 班级详情显示 “云研讨”
      Map<String, TrainChatGroupInfo> chatGroupInfoByClassIds = trainChatGroupService.getChatGroupInfoByClassIds(Collections.singletonList(id));
      logger.error("classId {},云研讨数据 {}", id,JSONObject.toJSONString(chatGroupInfoByClassIds));
      if (!chatGroupInfoByClassIds.isEmpty()){
        classInfo.setChatGroup(Boolean.TRUE);
        classInfo.setChatGroupId(chatGroupInfoByClassIds.get(id).getChatId());
        classInfo.setConversionId(chatGroupInfoByClassIds.get(id).getConversionId());
      }
    if (Objects.nonNull(classInfo)) {
      classInfo.setRestaurantsConfig(resConfig);
      classInfo.setGuestroomsConfig(guestroomsConfig);
      classInfo.setClassroomConfig(classroomsConfig);

    }

    return classInfo;
  }

  @Override
  public PagedResult<ClassInfo> findOrganization(int page, int pageSize, String orgaizationId) {
    // 为需求方联系人给人员表起别名
    com.zxy.product.train.jooq.tables.Member member2 = MEMBER.as("member2");
    Field<String> projectName = PROJECT.NAME.as("projectName");
    Field<String> organizationName = ORGANIZATION.NAME.as("organizationName");
    Field<String> contactName = member2.NAME.as("contactName");

    // 构建查询语句
    SelectOnConditionStep<Record> step = classDao.execute(x -> x
        .selectDistinct(Fields.start()
            .add(CLASS_INFO.ID, projectName, CLASS_INFO.ARRIVE_DATE, CLASS_INFO.RETURN_DATE,
                PROJECT.CODE,
                organizationName, contactName, MEMBER.FULL_NAME, MEMBER.PHONE_NUMBER,
                CLASS_INFO.STATUS,
                CLASS_INFO.IMPLEMENTATION_MONTH, CLASS_INFO.IMPLEMENTATION_YEAR,
                PROJECT.CONTACT_EMAIL,
                MEMBER.EMAIL, PROJECT.CONTACT_PHONE, CLASS_RESOURCE.REST_ROOM,
                CLASS_RESOURCE.DINING_ROOM, CLASS_RESOURCE.CLASSROOM, PROJECT.OBJECT, PROJECT.DAYS)
            .end())
        .from(CLASS_INFO).innerJoin(PROJECT).on(CLASS_INFO.PROJECT_ID.eq(PROJECT.ID))
        .leftJoin(ORGANIZATION)
        .on(PROJECT.ORGANIZATION_ID.eq(ORGANIZATION.ID)).leftJoin(member2)
        .on(member2.ID.eq(PROJECT.CONTACT_MEMBER_ID)).leftJoin(MEMBER)
        .on(MEMBER.ID.eq(CLASS_INFO.CLASS_TEACHER)).leftJoin(CLASS_RESOURCE)
        .on(CLASS_RESOURCE.CLASS_ID.eq(CLASS_INFO.ID)).leftJoin(ORGANIZATION_DETAIL)
        .on(PROJECT.ORGANIZATION_ID.eq(ORGANIZATION_DETAIL.SUB)).leftJoin(GRANT_DETAIL)
        .on(PROJECT.ORGANIZATION_ID.eq(GRANT_DETAIL.ORGANIZATION_ID)));
    // 获取行数
    Integer count = classDao
        .execute(e -> e.fetchCount(step.and(GRANT_DETAIL.URI.eq(ClassInfoService.URI))));
    // 获取列表
    List<ClassInfo> list = step.where(ORGANIZATION_DETAIL.ROOT.eq(orgaizationId))
        .limit((page - 1) * pageSize, pageSize).fetch(r -> {
          ClassInfo cf = r.into(CLASS_INFO).into(ClassInfo.class);
          cf.setCode(r.getValue(PROJECT.CODE));
          cf.setTeacher(r.getValue(MEMBER.FULL_NAME));// 班主任
          cf.setClassTeacherPhone(SM4Utils.decryptDataCBC(r.getValue(MEMBER.PHONE_NUMBER))); // 班主任电话
          cf.setContachEmail(r.getValue(PROJECT.CONTACT_EMAIL)); // 需求方邮箱
          cf.setTeacherEmail(SM4Utils.decryptDataCBC(r.getValue(MEMBER.EMAIL)));// 班主任邮箱
          cf.setContactPhone(r.getValue(PROJECT.CONTACT_PHONE));// 需求方电话
          cf.setRestRoom(r.getValue(CLASS_RESOURCE.REST_ROOM));// 客房
          cf.setDiningRoom(r.getValue(CLASS_RESOURCE.DINING_ROOM));// 餐厅
          cf.setClassRoom(r.getValue(CLASS_RESOURCE.CLASSROOM));// 教室
          cf.setTrainObject(r.getValue(PROJECT.OBJECT)); // 培训对象
          cf.setDays(r.getValue(PROJECT.DAYS));// 计划天数
          int temporaryStatus = 3;
          long date = System.currentTimeMillis();
          if (cf.getArriveDate() == null || date < cf.getArriveDate()) {
            temporaryStatus = 1;
          } else if (cf.getReturnDate() == null || date < cf.getReturnDate()) {
            temporaryStatus = 2;
          }
          cf.setStatus(temporaryStatus); // 班级状态
          return cf;
        });
    return PagedResult.create(count, list);
  }

  @Override
  public PagedResult<ClassInfo> findMember(int page, int pageSize, String orgaizationId) {
    // 为需求方联系人给人员表起别名
    com.zxy.product.train.jooq.tables.Member member2 = MEMBER.as("member2");
    Field<String> projectName = PROJECT.NAME.as("projectName");
    Field<String> organizationName = ORGANIZATION.NAME.as("organizationName");
    Field<String> contactName = member2.NAME.as("contactName");

    // 构建查询语句
    SelectOnConditionStep<Record> step = classDao.execute(x -> x
        .selectDistinct(Fields.start()
            .add(CLASS_INFO.ID, projectName, CLASS_INFO.ARRIVE_DATE, CLASS_INFO.RETURN_DATE,
                PROJECT.CODE,
                organizationName, contactName, MEMBER.FULL_NAME, MEMBER.PHONE_NUMBER,
                CLASS_INFO.STATUS,
                CLASS_INFO.IMPLEMENTATION_MONTH, CLASS_INFO.IMPLEMENTATION_YEAR,
                PROJECT.CONTACT_EMAIL,
                MEMBER.EMAIL, PROJECT.CONTACT_PHONE, CLASS_RESOURCE.REST_ROOM,
                CLASS_RESOURCE.DINING_ROOM, CLASS_RESOURCE.CLASSROOM, PROJECT.OBJECT, PROJECT.DAYS)
            .end())
        .from(CLASS_INFO).innerJoin(PROJECT).on(CLASS_INFO.PROJECT_ID.eq(PROJECT.ID))
        .leftJoin(ORGANIZATION)
        .on(PROJECT.ORGANIZATION_ID.eq(ORGANIZATION.ID)).leftJoin(member2)
        .on(member2.ID.eq(PROJECT.CONTACT_MEMBER_ID)).leftJoin(MEMBER)
        .on(MEMBER.ID.eq(CLASS_INFO.CLASS_TEACHER)).leftJoin(CLASS_RESOURCE)
        .on(CLASS_RESOURCE.CLASS_ID.eq(CLASS_INFO.ID)).leftJoin(ORGANIZATION_DETAIL)
        .on(PROJECT.ORGANIZATION_ID.eq(ORGANIZATION_DETAIL.SUB)).leftJoin(GRANT_DETAIL)
        .on(PROJECT.ORGANIZATION_ID.eq(GRANT_DETAIL.ORGANIZATION_ID)));
    // 获取行数
    Integer count = classDao
        .execute(e -> e.fetchCount(step.and(GRANT_DETAIL.URI.eq(ClassInfoService.URI))));
    // 获取列表
    List<ClassInfo> list = step.where(PROJECT.ORGANIZATION_ID.eq(orgaizationId))
        .limit((page - 1) * pageSize, pageSize).fetch(r -> {
          ClassInfo cf = r.into(CLASS_INFO).into(ClassInfo.class);
          cf.setCode(r.getValue(PROJECT.CODE));
          cf.setTeacher(r.getValue(MEMBER.FULL_NAME));// 班主任
          cf.setClassTeacherPhone(SM4Utils.decryptDataCBC(r.getValue(MEMBER.PHONE_NUMBER))); // 班主任电话
          cf.setContachEmail(r.getValue(PROJECT.CONTACT_EMAIL)); // 需求方邮箱
          cf.setTeacherEmail(SM4Utils.decryptDataCBC(r.getValue(MEMBER.EMAIL)));// 班主任邮箱
          cf.setContactPhone(r.getValue(PROJECT.CONTACT_PHONE));// 需求方电话
          cf.setRestRoom(r.getValue(CLASS_RESOURCE.REST_ROOM));// 客房
          cf.setDiningRoom(r.getValue(CLASS_RESOURCE.DINING_ROOM));// 餐厅
          cf.setClassRoom(r.getValue(CLASS_RESOURCE.CLASSROOM));// 教室
          cf.setTrainObject(r.getValue(PROJECT.OBJECT)); // 培训对象
          cf.setDays(r.getValue(PROJECT.DAYS));// 计划天数
          int temporaryStatus = 3;
          long date = System.currentTimeMillis();
          if (cf.getArriveDate() == null || date < cf.getArriveDate()) {
            temporaryStatus = 1;
          } else if (cf.getReturnDate() == null || date < cf.getReturnDate()) {
            temporaryStatus = 2;
          }
          cf.setStatus(temporaryStatus); // 班级状态
          return cf;
        });
    return PagedResult.create(count, list);
  }

  @Override
  public PagedResult<ClassInfo> frontFind(int page, int pageSize, Optional<String> memberId,
      Optional<String> MIScode,
      Optional<String> className, Optional<Integer> reachYear, Optional<Integer> reachMonth,
      Optional<Integer> classStatus, Optional<String> organization, Integer flag) {
    // 重命名
    Field<String> orgName = ORGANIZATION.NAME.as("orgName");
    Field<String> projectId = PROJECT.ID.as("projectId");
    Field<String> questionnaireId = RESEARCH_QUESTIONARY.ID.as("questionnaireId");
    // 构建查询语句
    SelectOnConditionStep<Record> step = classDao.execute(x -> x
        .select(Fields.start()
            .add(CLASS_INFO.ID, projectId, PROJECT.CODE, PROJECT.NAME, PROJECT.AMOUNT, orgName,
                MEMBER.FULL_NAME, PROJECT.CONTACT_PHONE, CLASS_INFO.ARRIVE_DATE,
                CLASS_INFO.RETURN_DATE,
                CLASS_INFO.STATUS, CLASS_INFO.TRAINEE_NUM, CLASS_INFO.NOTICE, questionnaireId,
                PROJECT.IS_MANUAL_FINISH)
            .end())
        .from(CLASS_INFO)
        .leftJoin(PROJECT).on(CLASS_INFO.PROJECT_ID.eq(PROJECT.ID))
        .leftJoin(ORGANIZATION).on(PROJECT.ORGANIZATION_ID.eq(ORGANIZATION.ID))
        .leftJoin(MEMBER).on(PROJECT.CONTACT_MEMBER_ID.eq(MEMBER.ID))
        .leftJoin(RESEARCH_QUESTIONARY)
        .on(RESEARCH_QUESTIONARY.CLASS_ID.eq(CLASS_INFO.ID).and(RESEARCH_QUESTIONARY.TYPE.eq(4)))
    );
    if (memberId.isPresent()) {
      step.leftJoin(CLASSSTAFF_CLASS).on(CLASSSTAFF_CLASS.CLASS_ID.eq(CLASS_INFO.ID));
    }
    // 组合条件
    Stream<Optional<Condition>> conditions = Stream.of(memberId.map(CLASSSTAFF_CLASS.MEMBER_ID::eq),
        MIScode.map(PROJECT.CODE::contains), className.map(PROJECT.NAME::contains),
        organization.map(ORGANIZATION.PATH::contains),
        reachYear.map(CLASS_INFO.IMPLEMENTATION_YEAR::eq),
        reachMonth.map(CLASS_INFO.IMPLEMENTATION_MONTH::eq),
        Optional.of(DELETE_FLASE).map(CLASS_INFO.DELETE_FLAG::eq));
    // 合并条件
    Condition c = conditions.filter(Optional::isPresent).map(Optional::get)
        .reduce((acc, item) -> acc.and(item))
        .orElse(DSL.trueCondition());
    SelectConditionStep<Record> condStep = step.where(c);
    if (flag == 0) {
      condStep.and(CLASSSTAFF_CLASS.DELETE_FLAG.eq(0));
    }
    if (classStatus.isPresent()) {
//            Long date = System.currentTimeMillis();
//            if (classStatus.get() == STATUS_FALSE) {
//                condStep.and(CLASS_INFO.ARRIVE_DATE.greaterThan(date).or(CLASS_INFO.ARRIVE_DATE.isNull()));
//            }
//            if (classStatus.get() == STATUS_ING) {
//                condStep.and(CLASS_INFO.ARRIVE_DATE.lessThan(date))
//                        .and(CLASS_INFO.RETURN_DATE.greaterThan(date).or(CLASS_INFO.RETURN_DATE.isNull()));
//            }
//            if (classStatus.get() == STATUS_TRUE) {
//                condStep.and(CLASS_INFO.RETURN_DATE.lessThan(date));
//            }
      condStep.and(CLASS_INFO.STATUS.eq(classStatus.get()));
    }

    // 获取行数
    Integer count = classDao.execute(e -> e.fetchCount(condStep));
    //排序
    if (classStatus.isPresent()) {
      if (classStatus.get() == STATUS_FALSE) {//未实施
        condStep.orderBy(CLASS_INFO.SORT.asc(), CLASS_INFO.ARRIVE_DATE.asc());
      }
      if (classStatus.get() == STATUS_ING) {//实施中
        condStep.orderBy(CLASS_INFO.SORT.asc(), CLASS_INFO.ARRIVE_DATE.asc());
      }
      if (classStatus.get() == STATUS_TRUE) {//已实施
        condStep.orderBy(CLASS_INFO.SORT.asc(), CLASS_INFO.ARRIVE_DATE.asc());
      }
    } else {
      condStep.orderBy(CLASS_INFO.SORT.asc(), CLASS_INFO.ARRIVE_DATE.desc());
    }
    // 获取列表
    List<ClassInfo> list = condStep.limit((page - 1) * pageSize, pageSize).fetch(r -> {
      ClassInfo ci = r.into(CLASS_INFO).into(ClassInfo.class);
      ci.setId(r.getValue(CLASS_INFO.ID));
      ci.setClassName(r.getValue(PROJECT.NAME));
//            long value = r.getValue(CLASS_INFO.ARRIVE_DATE) + 86400000;
//            long currentTimeMillis = System.currentTimeMillis();
//            if (value >= currentTimeMillis) {
//            	ci.setStatus(1);
//            } else {
//            	ci.setStatus(r.getValue(CLASS_INFO.STATUS));
//            }
      ci.setStatus(r.getValue(CLASS_INFO.STATUS));
      ci.setCode(r.getValue(PROJECT.CODE));
      ci.setAmount(r.getValue(PROJECT.AMOUNT));
      ci.setOrganization(r.getValue(orgName));
      ci.setContactPeople(r.getValue(MEMBER.FULL_NAME));
      ci.setContactPhone(r.getValue(PROJECT.CONTACT_PHONE));
      ci.setArriveDate(r.getValue(CLASS_INFO.ARRIVE_DATE));
      // ci.setReturnDate(r.getValue(CLASS_INFO.RETURN_DATE));
//            ci.setRegistNumber(r.getValue(TRAINEE.ID.countDistinct()));
      ci.setProjectId(r.getValue(projectId));
      ci.setQuestionaryId(r.getValue(questionnaireId));
      ci.setIsManualFinish(r.getValue(PROJECT.IS_MANUAL_FINISH));//是否手动结束：0-否，1-是
      return ci;
    });

    List<String> classIds = list.stream().filter(Objects::nonNull).map(ClassInfo::getId).filter(Objects::nonNull).collect(Collectors.toList());

    if (!CollectionUtils.isEmpty(classIds)) {
      Map<String, TrainChatGroupInfo> chatGroupInfoByClassIds = trainChatGroupService.getChatGroupInfoByClassIds(classIds);

      list.forEach(x ->
                   {
                     if (chatGroupInfoByClassIds.containsKey(x.getId())) {
                       x.setChatGroupId(chatGroupInfoByClassIds.get(x.getId()).getChatId());
                       x.setChatGroup(Boolean.TRUE);
                       x.setConversionId(chatGroupInfoByClassIds.get(x.getId()).getConversionId());
                     }
                   });
    }
    return PagedResult.create(count, list);
  }

  @Override
  public ClassInfo update(String id, Optional<String> classTeacher, Optional<String> MemberId,
      Optional<String> restRoom, Optional<String> classRoom, Optional<String> diningRoom,
      Optional<String> coverId, Optional<Integer> haveProvinceLeader,
      Optional<Integer> haveMinister,
      Optional<Integer> needGroupPhoto, Optional<Long> photoTime, Optional<Integer> needVideo,
      Optional<String> videoRequirement, Optional<Integer> needMakeCourse,
      Optional<String> courseVideoRequirement, Optional<Integer> needNet,
      Optional<String> tableType,
      Optional<String> otherRequirement, Optional<String> bannerId, Optional<Integer> confirm,
      Optional<String> teacherPhone, Optional<String> classInfoType, Optional<String> path,
      Optional<String> restaurantsIds,
      Optional<String> guestroomsIds) {
    ClassInfo classInfo = classDao.get(id);
    confirm.ifPresent(classInfo::setConfirm);
    if (classTeacher.isPresent() && !classTeacher.get().equals(classInfo.getClassTeacher())) {
      classstaffClassService.delTeacher(id, classInfo.getClassTeacher());
      int num = classstaffClassService.countByClass(id);
      if (num < 10) {
        classTeacher.ifPresent(x -> {
          classInfo.setClassTeacher(x);
          classstaffClassService.insertMasterTeacher(id, classTeacher.get());
        });
      }
    }
    teacherPhone.ifPresent(classInfo::setClassTeacherPhone);
    classInfoType.ifPresent(classInfo::setClassInfoType);
    classDao.update(classInfo);

    // 更新培训资源
//        Optional<ClassResource> classResource = resourceService.findByClassId(id);
//        ClassResource cr = null;
//        if (classResource.isPresent()) {
//            cr = classResource.get();
//            cr.setRestRoom(restRoom.orElse(null));
//            cr.setClassroom(classRoom.orElse(null));
//            cr.setDiningRoom(diningRoom.orElse(null));
//            classResourceDao.update(cr);
//        } else {
//            resourceService.insert(id, restRoom, diningRoom, classRoom, Optional.empty());
//        }
    classResourceDao.delete(CLASS_RESOURCE.CLASS_ID.eq(id));
    if (classRoom.isPresent()) {
      resourceService.insert(id, Optional.empty(), Optional.empty(), classRoom, Optional.empty());
    }
    if (restaurantsIds.isPresent()) {
      String[] rIds = restaurantsIds.get().split(",");
      for (String cr : rIds) {
        resourceService.insert(id, Optional.empty(), Optional.ofNullable(cr), Optional.empty(),
            Optional.empty());
      }
    }
    if (guestroomsIds.isPresent()) {
      String[] grIds = guestroomsIds.get().split(",");
      for (String cr : grIds) {
        resourceService.insert(id, Optional.ofNullable(cr), Optional.empty(), Optional.empty(),
            Optional.empty());
      }
    }
    // 更新班级详情表
    Optional<ClassDetail> classDetail = detailService.findByClassId(id);
    ClassDetail detail = null;
    if (classDetail.isPresent()) {
      detail = classDetail.get();
      coverId.ifPresent(detail::setCoverId);
      bannerId.ifPresent(detail::setBannerId);
      haveProvinceLeader.ifPresent(detail::setHaveProvinceLeader);
      haveMinister.ifPresent(detail::setHaveMinister);
      needGroupPhoto.ifPresent(detail::setNeedGroupPhoto);
      photoTime.ifPresent(detail::setPhotoTime);
      needVideo.ifPresent(detail::setNeedVideo);
      videoRequirement.ifPresent(detail::setVideoRequirement);
      needMakeCourse.ifPresent(detail::setNeedMakeCourse);
      courseVideoRequirement.ifPresent(detail::setCourseVideoRequirement);
      needNet.ifPresent(detail::setNeedNet);
      tableType.ifPresent(detail::setTableType);
      otherRequirement.ifPresent(detail::setOtherRequirement);
      detail.setOtherRequirement(otherRequirement.orElse(null));
      path.ifPresent(detail::setPath);
      detailDao.update(detail);
    } else {
      detailService
          .insert(id, coverId, bannerId, Optional.empty(), haveProvinceLeader, haveMinister,
              needGroupPhoto, photoTime, needVideo, videoRequirement, needMakeCourse,
              courseVideoRequirement,
              needNet, tableType, otherRequirement, Optional.empty(), Optional.empty(),
              Optional.empty(),
              Optional.empty(), path);
    }
    sender.send(MessageTypeContent.TRAIN_CLASS_UPDATE, MessageHeaderContent.ID, classInfo.getId());
    return classInfo;
  }

  @Override
  public ClassInfo findByProjectId(String projectId) {
    // 为需求方联系人给人员表起别名

    List<ClassroomConfiguration> roomList2 = classroomConfigurationDao.execute(x -> {
      return x.selectFrom(CLASSROOM_CONFIGURATION)
          .where(CLASSROOM_CONFIGURATION.TYPE_ID.eq(6)
              .and(CLASSROOM_CONFIGURATION.DELETE_FLAG.eq(ClassroomConfiguration.DELETE_FLASE)))
          .orderBy(CLASSROOM_CONFIGURATION.SORT.asc())
          .fetch().into(ClassroomConfiguration.class);
    });

    com.zxy.product.train.jooq.tables.Member member3 = MEMBER.as("member3");
    com.zxy.product.train.jooq.tables.ConfigurationValue configurationValue = CONFIGURATION_VALUE
        .as("configurationValue");
    com.zxy.product.train.jooq.tables.ConfigurationValue configurationValue1 = CONFIGURATION_VALUE
        .as("configurationValue1");
    // 拼写Sql
    SelectConditionStep<Record> step = classDao.execute(x -> x
        .selectDistinct(Fields.start()
            .add(CLASS_INFO.ID, CLASS_INFO.ARRIVE_DATE, CLASS_INFO.RETURN_DATE,
                CLASS_INFO.CLASS_INFO_TYPE,
                CLASS_INFO.IMPLEMENTATION_YEAR, CLASS_INFO.IMPLEMENTATION_MONTH, PROJECT.CODE,
                PROJECT.NAME, PROJECT.ADDRESS, ORGANIZATION.NAME, member3.FULL_NAME,
                MEMBER.PHONE_NUMBER, MEMBER.EMAIL, CLASS_INFO.STATUS, PROJECT.CONTACT_EMAIL,
                configurationValue.NAME, configurationValue1.NAME,
                PROJECT.CONTACT_PHONE, PROJECT.OBJECT, CLASS_INFO.NOTICE)
            .add(CLASS_DETAIL)
//                        .add(CLASS_RESOURCE)
//                        .add(CLASSROOM_CONFIGURATION.CLASSROOM)
            .add(CONFIGURATION_VALUE.NAME)
            .add(CLASS_INFO.CONFIRM).end())
        .from(CLASS_INFO).innerJoin(PROJECT).on(CLASS_INFO.PROJECT_ID.eq(PROJECT.ID))
        .leftJoin(CLASS_DETAIL)
        .on(CLASS_INFO.ID.eq(CLASS_DETAIL.CLASS_ID)).leftJoin(ORGANIZATION)
        .on(PROJECT.ORGANIZATION_ID.eq(ORGANIZATION.ID)).leftJoin(member3)
        .on(member3.ID.eq(PROJECT.CONTACT_MEMBER_ID)).leftJoin(MEMBER)
        .on(MEMBER.ID.eq(CLASS_INFO.CLASS_TEACHER))
//                .leftJoin(CLASS_RESOURCE)
//                .on(CLASS_RESOURCE.CLASS_ID.eq(CLASS_INFO.ID))
//                .leftJoin(CLASSROOM_CONFIGURATION)
//                .on(CLASSROOM_CONFIGURATION.ID.eq(CLASS_RESOURCE.CLASSROOM))
        .leftJoin(configurationValue)
        .on(configurationValue.ID.eq(PROJECT.TYPE_ID))
        .leftJoin(configurationValue1)
        .on(configurationValue1.ID.eq(PROJECT.COST))
        .leftJoin(CONFIGURATION_VALUE)
        .on(CONFIGURATION_VALUE.ID.eq(PROJECT.ADDRESS))
        .where(PROJECT.ID.eq(projectId).and(CLASS_INFO.DELETE_FLAG.eq(ClassInfo.DELETE_FLASE))));
    // 输出页面显示数据
    ClassInfo classInfo = step.fetchOne(r -> {
      ClassInfo cf = new ClassInfo();
      cf.setId(r.getValue(CLASS_INFO.ID));
      cf.setImplementationMonth(r.getValue(CLASS_INFO.IMPLEMENTATION_MONTH));
      cf.setImplementationYear(r.getValue(CLASS_INFO.IMPLEMENTATION_YEAR));
      cf.setReturnDate(r.getValue(CLASS_INFO.RETURN_DATE));
      cf.setArriveDate(r.getValue(CLASS_INFO.ARRIVE_DATE));
      cf.setClassName(r.getValue(PROJECT.NAME));
      cf.setCode(r.getValue(PROJECT.CODE));
      cf.setOrganization(r.getValue(ORGANIZATION.NAME)); // 需求方
      cf.setTeacher(r.getValue(MEMBER.FULL_NAME));// 班主任
      cf.setClassTeacherPhone(SM4Utils.decryptDataCBC(r.getValue(MEMBER.PHONE_NUMBER))); // 班主任电话
      cf.setContachEmail(r.getValue(PROJECT.CONTACT_EMAIL)); // 需求方邮箱
      cf.setTeacherEmail(SM4Utils.decryptDataCBC(r.getValue(MEMBER.EMAIL)));// 班主任邮箱
      cf.setContactPeople(r.getValue(member3.FULL_NAME));// 需求联系人
      cf.setContactPhone(r.getValue(PROJECT.CONTACT_PHONE));// 需求方电话
//            cf.setRestRoom(r.getValue(CLASS_RESOURCE.REST_ROOM));// 客房
//            cf.setDiningRoom(r.getValue(CLASS_RESOURCE.DINING_ROOM));// 餐厅
//            cf.setClassRoom(r.getValue(CLASS_RESOURCE.CLASSROOM));// 教室
//            cf.setClassRoomName(r.getValue(CLASSROOM_CONFIGURATION.CLASSROOM));// 教室名称
      cf.setTrainObject(r.getValue(PROJECT.OBJECT)); // 培训对象
      cf.setStatus(r.getValue(CLASS_INFO.STATUS)); // 班级状态
      cf.setAddress(r.getValue(CONFIGURATION_VALUE.NAME));// 学习地点
      cf.setType(r.getValue(configurationValue.NAME));
      cf.setCost(r.getValue(configurationValue1.NAME));
      cf.setConfirm(r.getValue(CLASS_INFO.CONFIRM));
      cf.setClassInfoType(r.getValue(CLASS_INFO.CLASS_INFO_TYPE));
      cf.setNotice(r.getValue(CLASS_INFO.NOTICE));//班级是否已发布
      ClassDetail classDetail = r.into(ClassDetail.class);
      cf.setClassDetail(classDetail);
      return cf;
    });

    List<ConfigurationValue> restaurantsConfig = configurationValueDao
        .execute(x -> x.select(Fields.start().add(CONFIGURATION_VALUE).end())
            .from(CONFIGURATION_VALUE)
            .innerJoin(CLASS_RESOURCE).on(CLASS_RESOURCE.DINING_ROOM.eq(CONFIGURATION_VALUE.ID))
            .where(CLASS_RESOURCE.CLASS_ID.eq(classInfo.getId()))
            .and(CONFIGURATION_VALUE.DELETE_FLAG.eq(0))
            .and(CONFIGURATION_VALUE.TYPE_ID.eq(14))
            .orderBy(CONFIGURATION_VALUE.SORT.asc())
        ).fetch(b -> {
          ConfigurationValue taskReviewer = b.into(ConfigurationValue.class);
          taskReviewer.setId(b.getValue(CONFIGURATION_VALUE.ID));
          taskReviewer.setName(b.getValue(CONFIGURATION_VALUE.NAME));
          return taskReviewer;
        });

    List<ConfigurationValue> guestroomsConfig = configurationValueDao
        .execute(x -> x.select(Fields.start().add(CONFIGURATION_VALUE).end())
            .from(CONFIGURATION_VALUE)
            .innerJoin(CLASS_RESOURCE).on(CLASS_RESOURCE.REST_ROOM.eq(CONFIGURATION_VALUE.ID))
            .where(CLASS_RESOURCE.CLASS_ID.eq(classInfo.getId()))
            .and(CONFIGURATION_VALUE.DELETE_FLAG.eq(0))
            .and(CONFIGURATION_VALUE.TYPE_ID.eq(15))
            .orderBy(CONFIGURATION_VALUE.SORT.asc())
        ).fetch(b -> {
          ConfigurationValue taskReviewer = b.into(ConfigurationValue.class);
          taskReviewer.setId(b.getValue(CONFIGURATION_VALUE.ID));
          taskReviewer.setName(b.getValue(CONFIGURATION_VALUE.NAME));
          return taskReviewer;
        });

    List<ClassroomConfiguration> classroomsConfig = classroomConfigurationDao
        .execute(x -> x.select(Fields.start().add(CLASSROOM_CONFIGURATION).end())
            .from(CLASSROOM_CONFIGURATION)
            .innerJoin(CLASS_RESOURCE).on(CLASS_RESOURCE.CLASSROOM.eq(CLASSROOM_CONFIGURATION.ID))
            .where(CLASS_RESOURCE.CLASS_ID.eq(classInfo.getId()))
            .and(CLASSROOM_CONFIGURATION.DELETE_FLAG.eq(0))
            .orderBy(CLASSROOM_CONFIGURATION.SORT.asc())
        ).fetch(b -> {
          ClassroomConfiguration taskReviewer = b.into(ClassroomConfiguration.class);
          taskReviewer.setId(b.getValue(CLASSROOM_CONFIGURATION.ID));
          taskReviewer.setClassroom(b.getValue(CLASSROOM_CONFIGURATION.CLASSROOM));
          return taskReviewer;
        });
    if (classroomsConfig != null && classroomsConfig.size() > 0) {
      classInfo.setClassroomConfig(classroomsConfig);
    } else {
      if (roomList2 != null && roomList2.size() > 0) {
        classInfo.setClassRoomName(roomList2.get(0).getClassroom());
      }
    }
    classInfo.setRestaurantsConfig(restaurantsConfig);
    classInfo.setGuestroomsConfig(guestroomsConfig);

    return classInfo;
  }

  @Override
  public ClassInfo insert(String projectId, Optional<String> classTeacherPhone,
      Optional<String> classTeacher,
      Optional<Long> arriveDate, Optional<Long> returnDate, Optional<Integer> isOutSide,
      Optional<String> surveyType, Optional<String> target, Optional<String> level,
      Optional<String> studentType,
      Optional<String> simpleType, Optional<Integer> isPlan, Optional<Integer> implementationYear,
      Optional<Integer> implementationMonth, Optional<Integer> status,
      Optional<String> createMemberId) {
    ClassInfo classInfo = new ClassInfo();
    classInfo.forInsert();
    classInfo.setProjectId(projectId);
    classTeacherPhone.ifPresent(classInfo::setClassTeacherPhone);
    classTeacher.ifPresent(classInfo::setClassTeacher);
    arriveDate.ifPresent(classInfo::setArriveDate);
    returnDate.ifPresent(classInfo::setReturnDate);
    isOutSide.ifPresent(classInfo::setIsOutside);
    surveyType.ifPresent(classInfo::setSurveyType);
    target.ifPresent(classInfo::setTarget);
    level.ifPresent(classInfo::setClassInfoType);
    studentType.ifPresent(classInfo::setStudentType);
    simpleType.ifPresent(classInfo::setSimpleType);
    isPlan.ifPresent(classInfo::setIsPlan);
    status.ifPresent(classInfo::setStatus);
    implementationYear.ifPresent(classInfo::setImplementationYear);
    implementationMonth.ifPresent(classInfo::setImplementationMonth);
    createMemberId.ifPresent(classInfo::setCreateMember);
    classInfo.setDeleteFlag(ClassInfo.DELETE_FLASE);
    ClassInfo c = classDao.insert(classInfo);
    sender.send(MessageTypeContent.TRAIN_CLASS_INSERT, MessageHeaderContent.ID, classInfo.getId());
    return c;
  }

  @Override
  public int del(String id) {
    int status = classDao.delete(id);
    deleteDataTrainCommonDao.insert(DeleteDataTrain.getDeleteData(CLASS_INFO.getName(), id,""));

    sender.send(MessageTypeContent.TRAIN_CLASS_DELETE, MessageHeaderContent.ID, id);
    sender.send(MessageTypeContent.TRAIN_CLASS_DELETE_SMART_CAMPUS, MessageHeaderContent.ID, id);
    return status;
  }

  @Override
  public ClassInfo findSingleByProjectId(String projectId) {
    Optional<ClassInfo> classInfo = classDao.execute(x -> x
        .select(Fields.start().add(CLASS_INFO).add(PROJECT.ORGANIZATION_ID, PROJECT.NAME,PROJECT.IS_PARTY_CADRE).end())
        .from(CLASS_INFO).leftJoin(PROJECT).on(CLASS_INFO.PROJECT_ID.eq(PROJECT.ID))
        .where(PROJECT.ID.eq(projectId))
        .and(PROJECT.DELETE_FLAG.eq(Project.DELETE_FLASE))
        .and(CLASS_INFO.DELETE_FLAG.eq(ClassInfo.DELETE_FLASE)))
        .fetchOptional(r -> {
          ClassInfo c = r.into(ClassInfo.class);
          c.setClassName(r.getValue(PROJECT.NAME));
          c.setOrganizationId(r.getValue(PROJECT.ORGANIZATION_ID));
          c.setIsPartyCadre(r.getValue(PROJECT.IS_PARTY_CADRE));
          return c;
        });

    return classInfo.isPresent() ? classInfo.get() : new ClassInfo();
  }

  @Override
  public ClassInfo findClassAndProjectByClassId(String classId) {
    // 拼写Sql
    SelectConditionStep<Record> step = classDao.execute(x -> x
        .selectDistinct(Fields.start().add(CLASS_INFO)
            .add(PROJECT.CODE, PROJECT.AMOUNT, PROJECT.NAME, PROJECT.ADDRESS, PROJECT.CONTACT_EMAIL,
                PROJECT.DAYS, PROJECT.CONTACT_PHONE, PROJECT.OBJECT)
            .end())
        .from(CLASS_INFO).innerJoin(PROJECT).on(CLASS_INFO.PROJECT_ID.eq(PROJECT.ID))
        .where(CLASS_INFO.ID.eq(classId).and(CLASS_INFO.DELETE_FLAG.eq(ClassInfo.DELETE_FLASE))));
    // 输出页面显示数据
    ClassInfo classInfo = step.fetchOne(r -> {
      ClassInfo cf = r.into(ClassInfo.class);
      cf.setClassName(r.getValue(PROJECT.NAME));
      cf.setCode(r.getValue(PROJECT.CODE));
      cf.setContachEmail(r.getValue(PROJECT.CONTACT_EMAIL)); // 需求方邮箱
      cf.setContactPhone(r.getValue(PROJECT.CONTACT_PHONE));// 需求方电话
      cf.setTrainObject(r.getValue(PROJECT.OBJECT)); // 培训对象
      cf.setAddress(r.getValue(PROJECT.ADDRESS));
      cf.setAmount(r.getValue(PROJECT.AMOUNT));
      cf.setDays(r.getValue(PROJECT.DAYS));
      return cf;
    });
    return classInfo;
  }

  public Date[] DateMonth(String date) {
    Date[] da = new Date[2];
    Date dateDate = null;
    Date dateDate1 = null;
    if (date != null && !("").equals(date)) {
      String dates[] = date.split("-");
      Integer flag = 30;
      Integer dateI = Integer.parseInt(dates[1]);
      if (dateI == 1 || dateI == 3 || dateI == 5 || dateI == 7 || dateI == 8 || dateI == 10
          || dateI == 12) {
        flag = 31;
      } else if (dateI == 2) {
        flag = 28;
        int num = Integer.parseInt(dates[0]);
        if (num % 4 == 0) {
          flag = 29;
        }
      }
      String flagS = flag.toString();
      String dateTime = dates[0] + "-" + dates[1] + "-" + flagS;
      String dateTime1 = dates[0] + "-" + dates[1] + "-01";
      SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
      try {
        dateDate = sdf.parse(dateTime);
        dateDate1 = sdf.parse(dateTime1);
        da[0] = dateDate;
        da[1] = dateDate1;
      } catch (ParseException e) {
        e.printStackTrace();
      }
    }

    return da;
  }

  @Override
  public List<Settlement> findMonthMember(String date, List<String> organizationIds, Integer num) {
    List<Settlement> list = new ArrayList<>();
    com.zxy.product.train.jooq.tables.Organization organization2 = ORGANIZATION.as("organization2");
    com.zxy.product.train.jooq.tables.Organization organization3 = ORGANIZATION.as("organization3");
    Field<String> orgName = organization3.NAME.as("orgName");
    Field<String> proName = PROJECT.NAME.as("proName");
    // 重命名组织表
    List<GroupConfigurationValue> glist = groupConfigurationValueDao.execute(e -> {
      List<GroupConfigurationValue> all = e.selectFrom(GROUP_CONFIGURATION_VALUE)
          .fetch()
          .into(GroupConfigurationValue.class);
      return all;
    });
    //查深度为3的组织
    String dates[] = date.split("-");
//        Date [] dates = this.DateMonth(date)
    SelectFinalStep<Record> step = settlementCommonDao
        .execute(
            x -> x.select(Fields.start().add(SETTLEMENT).add(TRAINEE).add(CONFIGURATION_VALUE.NAME)
                .add(organization2.NAME, orgName, PROJECT.CODE, proName, organization2.PATH,
                    PROJECT.DAYS)
                .add(MEMBER).add(ORGANIZATION).end())
                .from(CLASS_INFO).innerJoin(SETTLEMENT).on(SETTLEMENT.CLASS_ID.eq(CLASS_INFO.ID))
                .innerJoin(PROJECT).on(CLASS_INFO.PROJECT_ID.eq(PROJECT.ID)).leftJoin(ORGANIZATION)
                .on(PROJECT.ORGANIZATION_ID.eq(ORGANIZATION.ID))
                .and(PROJECT.ORGANIZATION_ID.in(organizationIds))
                .innerJoin(TRAINEE).on(TRAINEE.CLASS_ID.eq(CLASS_INFO.ID))
                .and(TRAINEE.DELETE_FLAG.eq(Trainee.DELETE_FLASE))
                .and(TRAINEE.TYPE.eq(TRAINEE_TYPE_FORMAL)).and(TRAINEE.AUDIT_STATUS.eq(AUDIT_AGREE))
                .leftJoin(MEMBER).on(MEMBER.ID.eq(TRAINEE.MEMBER_ID))
                .leftJoin(CONFIGURATION_VALUE).on(CONFIGURATION_VALUE.ID.eq(PROJECT.TYPE_ID))
                .leftJoin(organization2).on(MEMBER.ORGANIZATION_ID.eq(organization2.ID))
                .leftJoin(organization3).on(organization3.ID.eq(organization2.COMPANY_ID))
                .where(SETTLEMENT.CREATE_MOUTH.eq(date))
        );
    // 获取列表
    list = step.fetch(r -> {
      Settlement settlement = r.into(SETTLEMENT).into(Settlement.class);
      Project pr = new Project();
      pr.setName(r.getValue(proName));
      pr.setCode(r.getValue(PROJECT.CODE));
      pr.setDays(r.getValue(PROJECT.DAYS));
      pr.setTypeId(r.getValue(CONFIGURATION_VALUE.NAME));
      Organization organization = r.into(ORGANIZATION).into(Organization.class);
      settlement.setOrganization(organization);
      settlement.setOrganiaztionName(organization.getName());
      settlement.setOrganizationId(r.getValue(organization2.NAME));
      settlement.setSettleOrganizationName("");
      settlement.setSettlementCompany(r.getValue(orgName));
      settlement.setProject(pr);
      settlement.setMonth(dates[1]);
      settlement.setPath(r.getValue(organization2.PATH));
//            settlement.setRelation(r.getValue(jobName));
      Trainee trainee = r.into(TRAINEE).into(Trainee.class);
      settlement.setNation(trainee.getNation());
      Member member = r.into(MEMBER).into(Member.class);
      trainee.setMember(member);
      settlement.setTrainee(trainee);
      return settlement;
    });
    // 所属职务
    Set<String> positionIdSet = new HashSet<>();
    list.forEach(r -> {
      String path = r.getPath();
      String cName = r.getSettlementCompany();
      if (glist != null && glist.size() > 0) {
        for (GroupConfigurationValue li : glist) {
          if (path != null && path.contains(li.getPath())) {
            if (num.equals(2) && li.getGroupId().equals("1")) {
              r.setSettleOrganizationName("集团公司");
            } else {
              r.setSettleOrganizationName(li.getShortName());
            }
            break;
          } else {
            r.setSettleOrganizationName(cName);
          }
        }
      } else {
        r.setSettleOrganizationName(cName);
      }
      positionIdSet.add(r.getTrainee().getMember().getMajorPositionId());
    });
    Map<String, Position> positionMap = positionDao
        .execute(e -> e.select(Fields.start().add(POSITION).add(JOB.NAME).end())
            .from(POSITION).innerJoin(JOB).on(POSITION.JOB_ID.eq(JOB.ID))
            .where(POSITION.ID.in(positionIdSet)).fetch(record -> {
              Position position = record.into(POSITION).into(Position.class);
              position.setJobName(record.getValue(JOB.NAME));
              return position;
            }).stream().collect(Collectors.toMap(Position::getId, p -> p)));
    list.forEach(r -> {
      if (r.getTrainee().getMember().getMajorPositionId() != null
          && positionMap.get(r.getTrainee().getMember().getMajorPositionId()) != null) {
        r.setRelation(
            positionMap.get(r.getTrainee().getMember().getMajorPositionId()).getJobName());
      }
    });
    return list;

  }

  private Map<String, Organization> getOrganizationMap(Set<String> idSet) {
    return orgDao.execute(e -> e.select(
        Fields.start().add(ORGANIZATION.ID).add(ORGANIZATION.NAME).add(ORGANIZATION.COMPANY_ID)
            .end())
        .from(ORGANIZATION)
        .where(ORGANIZATION.ID.in(idSet))
        .fetch(record -> {
          Organization organization = new Organization();
          organization.setId(record.get(ORGANIZATION.ID));
          organization.setName(record.get(ORGANIZATION.NAME));
          organization.setCompanyId(record.get(ORGANIZATION.COMPANY_ID));
          return organization;
        }).stream().collect(Collectors.toMap(Organization::getId, o -> o)));
  }


  private Map<String, Map<String, List<ConfigurationValue>>> getRoom(List<String> classIds) {

    Map<String, Map<String, List<ConfigurationValue>>> resultMap = new HashMap<>();
    //食堂
    Map<String, List<ConfigurationValue>> restaurantMap = new HashMap<>();
    //客房
    Map<String, List<ConfigurationValue>> guestroomsMap = new HashMap<>();
    //班级id不为空
    if (!CollectionUtils.isEmpty(classIds)) {
      List<ConfigurationValue> restaurantsConfig = configurationValueDao
              .execute(x -> x.select(Fields.start().add(CONFIGURATION_VALUE).add(CLASS_RESOURCE.CLASS_ID).end())
                      .from(CONFIGURATION_VALUE)
                      .innerJoin(CLASS_RESOURCE).on(CLASS_RESOURCE.DINING_ROOM.eq(CONFIGURATION_VALUE.ID))
                      .where(CLASS_RESOURCE.CLASS_ID.in(classIds))
                      .and(CONFIGURATION_VALUE.DELETE_FLAG.eq(0))
                      .and(CONFIGURATION_VALUE.TYPE_ID.eq(14))
                      .orderBy(CONFIGURATION_VALUE.SORT.asc())
              ).fetch(b -> {
                ConfigurationValue taskReviewer = b.into(ConfigurationValue.class);
                taskReviewer.setId(b.getValue(CONFIGURATION_VALUE.ID));
                taskReviewer.setName(b.getValue(CONFIGURATION_VALUE.NAME));
                taskReviewer.setClaId(b.getValue(CLASS_RESOURCE.CLASS_ID));
                return taskReviewer;
              });
      if (!CollectionUtils.isEmpty(restaurantsConfig)) {
        restaurantMap = restaurantsConfig.stream().collect(Collectors.groupingBy(ConfigurationValue::getClaId));
      }
      List<ConfigurationValue> guestroomsConfig = configurationValueDao
              .execute(x -> x.select(Fields.start().add(CONFIGURATION_VALUE).add(CLASS_RESOURCE.CLASS_ID).end())
                      .from(CONFIGURATION_VALUE)
                      .innerJoin(CLASS_RESOURCE).on(CLASS_RESOURCE.REST_ROOM.eq(CONFIGURATION_VALUE.ID))
                      .where(CLASS_RESOURCE.CLASS_ID.in(classIds))
                      .and(CONFIGURATION_VALUE.DELETE_FLAG.eq(0))
                      .and(CONFIGURATION_VALUE.TYPE_ID.eq(15))
                      .orderBy(CONFIGURATION_VALUE.SORT.asc())
              ).fetch(b -> {
                ConfigurationValue taskReviewer = b.into(ConfigurationValue.class);
                taskReviewer.setId(b.getValue(CONFIGURATION_VALUE.ID));
                taskReviewer.setName(b.getValue(CONFIGURATION_VALUE.NAME));
                taskReviewer.setClaId(b.getValue(CLASS_RESOURCE.CLASS_ID));
                return taskReviewer;
              });

      if (!CollectionUtils.isEmpty(guestroomsConfig)) {
        guestroomsMap = guestroomsConfig.stream().collect(Collectors.groupingBy(ConfigurationValue::getClaId));
      }
      if (Objects.nonNull(restaurantMap) && restaurantMap.size() > 0) {
        resultMap.put("restaurantMap", restaurantMap);
      }
      if (Objects.nonNull(guestroomsMap) && guestroomsMap.size() > 0) {
        resultMap.put("guestroomsMap", guestroomsMap);
      }
    }
    return resultMap;
  }


  /**
   * 设置
   * @param classroomsMap
   * @param room
   * @param r
   */
  private void setRoom(Map<String, List<ClassroomConfiguration>> classroomsMap,Map<String, Map<String, List<ConfigurationValue>>> room, Settlement r){
    String classId = r.getClassId();
    //设置教室
    List<ClassroomConfiguration> classroomConfigurations = classroomsMap.get(classId);
    if(!CollectionUtils.isEmpty(classroomConfigurations)) {
      List<String> rooms = classroomConfigurations.stream().map(ClassroomConfiguration::getClassroom).collect(Collectors.toList());
      if(!CollectionUtils.isEmpty(rooms)) {
        r.setClassrooms(StringUtils.join(rooms, CommonConstant.SEPARATOR_COMMA));
      }
    }

    //设置餐厅客房
    if (Objects.nonNull(room) && room.size()>0){
      Map<String, List<ConfigurationValue>> restaurantMap = room.get("restaurantMap");
      Map<String, List<ConfigurationValue>> guestroomsMap = room.get("guestroomsMap");
      if(Objects.nonNull(restaurantMap) && restaurantMap.size()>0){
        List<ConfigurationValue> configurationValues = restaurantMap.get(classId);
        if(!CollectionUtils.isEmpty(configurationValues)) {
          List<String> restaurantRooms = configurationValues.stream().map(ConfigurationValue::getName).collect(Collectors.toList());
          if(!CollectionUtils.isEmpty(restaurantRooms)) {
            r.setRestaurantRooms(StringUtils.join(restaurantRooms, CommonConstant.SEPARATOR_COMMA));
          }
        }
      }
      //设置客房
      if(Objects.nonNull(guestroomsMap) && guestroomsMap.size()>0){
        List<ConfigurationValue> configurationValues = guestroomsMap.get(classId);
        if(!CollectionUtils.isEmpty(configurationValues)) {
          List<String> guestroomsRooms = configurationValues.stream().map(ConfigurationValue::getName).collect(Collectors.toList());
          if(!CollectionUtils.isEmpty(guestroomsRooms)) {
            r.setGuestroomsRooms(StringUtils.join(guestroomsRooms, CommonConstant.SEPARATOR_COMMA));
          }
        }
      }
    }
  }
  @Override
  public List<Settlement> findMonth(String date, List<String> organizationIds) {
    List<Settlement> list = new ArrayList<>();
    Date[] dates = this.DateMonth(date);
    ArrayList list1 = new ArrayList();
    // 构建查询语句
    SelectFinalStep<Record> step = settlementCommonDao
        .execute(x -> x.selectDistinct(
            Fields.start().add(SETTLEMENT).add(PROJECT.CODE, PROJECT.NAME, PROJECT.DAYS)
                    .add(CONFIGURATION_VALUE.NAME.as("addressName"))
                .add(CLASS_INFO.ID, CLASS_INFO.ARRIVE_DATE,CLASS_INFO.RETURN_DATE,CLASS_INFO.CLASS_TEACHER).add(ORGANIZATION.NAME).end())
            .from(SETTLEMENT).innerJoin(CLASS_INFO).on(SETTLEMENT.CLASS_ID.eq(CLASS_INFO.ID))
            .innerJoin(PROJECT).on(CLASS_INFO.PROJECT_ID.eq(PROJECT.ID)).leftJoin(ORGANIZATION)
            .on(PROJECT.ORGANIZATION_ID.eq(ORGANIZATION.ID))
//            .and(PROJECT.ORGANIZATION_ID.in(organizationIds))
            .leftJoin(GRANT_DETAIL).on(GRANT_DETAIL.ORGANIZATION_ID.eq(PROJECT.ORGANIZATION_ID))
            .leftJoin(ORGANIZATION_DETAIL)
            .on(ORGANIZATION_DETAIL.SUB.eq(GRANT_DETAIL.ORGANIZATION_ID))
            .leftJoin(CONFIGURATION_VALUE).on(CONFIGURATION_VALUE.ID.eq(PROJECT.ADDRESS))
            .where(SETTLEMENT.CREATE_MOUTH.eq(date)));
    list = step.fetch(r -> {
      Settlement settlement = r.into(SETTLEMENT).into(Settlement.class);
      Project pr = new Project();
      pr.setName(r.getValue(PROJECT.NAME));
      pr.setCode(r.getValue(PROJECT.CODE));
      pr.setDays(r.getValue(PROJECT.DAYS));
      pr.setAddressName(r.getValue(CONFIGURATION_VALUE.NAME.as("addressName")));
      Organization organization = r.into(ORGANIZATION).into(Organization.class);
      settlement.setOrganiaztionName(organization.getName());
      list1.add(r.getValue(CLASS_INFO.ID));
      settlement.setProject(pr);
      ClassInfo classInfo = r.into(CLASS_INFO).into(ClassInfo.class);
      settlement.setClassInfo(classInfo);
      return settlement;
    });

    List<ClassroomConfiguration> classroomsConfig = classroomConfigurationDao
              .execute(x -> x.select(Fields.start().add(CLASSROOM_CONFIGURATION).add(CLASS_RESOURCE.CLASS_ID).end())
                      .from(CLASSROOM_CONFIGURATION)
                      .innerJoin(CLASS_RESOURCE).on(CLASS_RESOURCE.CLASSROOM.eq(CLASSROOM_CONFIGURATION.ID))
                      .where(CLASS_RESOURCE.CLASS_ID.in(list1))
                      .and(CLASSROOM_CONFIGURATION.DELETE_FLAG.eq(0))
                      .orderBy(CLASSROOM_CONFIGURATION.SORT.asc())
              ).fetch(b -> {
                ClassroomConfiguration taskReviewer = b.into(ClassroomConfiguration.class);
                taskReviewer.setId(b.getValue(CLASSROOM_CONFIGURATION.ID));
                taskReviewer.setClassroom(b.getValue(CLASSROOM_CONFIGURATION.CLASSROOM));
                taskReviewer.setClaId(b.getValue(CLASS_RESOURCE.CLASS_ID));
                return taskReviewer;
              });
    Map<String, List<ClassroomConfiguration>> classroomsMap;
    if (!CollectionUtils.isEmpty(classroomsConfig)) {
      classroomsMap = classroomsConfig.stream().collect(Collectors.groupingBy(ClassroomConfiguration::getClaId));
    } else {
      classroomsMap = new HashMap<>();
    }
    //得到餐厅跟客房
    Map<String, Map<String, List<ConfigurationValue>>> room = getRoom(list1);
    //得到班主任
    List<ClassstaffClass> classMember = classstaffClassService.findByClassMember(list1);
    Map<String, List<ClassstaffClass>> classMemberMap;
    if(!CollectionUtils.isEmpty(classMember)){
      classMemberMap = classMember.stream().collect(Collectors.groupingBy(ClassstaffClass::getClassId));
    } else {
        classMemberMap = new HashMap<>();
    }

      Map<String, Integer> map = traineeDao.execute(
        r -> r.select(Fields.start().add(TRAINEE.MEMBER_ID.count(), TRAINEE.CLASS_ID).end())
            .from(TRAINEE).where(TRAINEE.TYPE.eq(TRAINEE_TYPE_FORMAL))
            .and(TRAINEE.DELETE_FLAG.eq(DELETE_FLASE).and(TRAINEE.AUDIT_STATUS.eq(AUDIT_AGREE))
                .and(TRAINEE.CLASS_ID.in(list1))).groupBy(TRAINEE.CLASS_ID).fetch(record -> {
              Trainee trainee = new Trainee();
              trainee.setClassId(record.getValue(TRAINEE.CLASS_ID));
              trainee.setNum(record.getValue(TRAINEE.MEMBER_ID.count()));
              return trainee;
            }).stream()
            .collect(Collectors.toMap(Trainee::getClassId, trainee -> trainee.getNum())));
    list.forEach(r -> {
      String classId = r.getClassId();
      r.getProject().setAmount(map.get(classId));
      setRoom(classroomsMap, room, r);
      //设置班主任
      if(Objects.nonNull(classMemberMap) && classMemberMap.size() > 0){
        List<ClassstaffClass> classstaffClasses = classMemberMap.get(classId);
        //如果存在班主任
        if(!CollectionUtils.isEmpty(classstaffClasses)){
          //大于两条，只取前两条
          if(classstaffClasses.size() > CommonConstant.TWO){
            classstaffClasses = classstaffClasses.subList(0, 2);
          }
          r.setClassTeacher(StringUtils.join(classstaffClasses.stream().map(ClassstaffClass::getMemberName).collect(Collectors.toList()), CommonConstant.SEPARATOR_COMMA));
        }
      }
    });
    return list;
  }

  @Override
  public List<ClassInfo> findClassNameAndOrganizationByClassId(String ids) {
    Field<String> organizationName = ORGANIZATION.NAME.as("organizationName");
    String id[] = ids.split(",");
    SelectOnConditionStep<Record> step = classDao.execute(x -> x
        .selectDistinct(Fields.start()
            .add(CLASS_INFO.ID, PROJECT.NAME, PROJECT.ORGANIZATION_ID, organizationName).end())
        .from(CLASS_INFO).leftJoin(PROJECT).on(PROJECT.ID.eq(CLASS_INFO.PROJECT_ID))
        .leftJoin(ORGANIZATION)
        .on(PROJECT.ORGANIZATION_ID.eq(ORGANIZATION.ID)));
    List<ClassInfo> list = step.where(CLASS_INFO.ID.in(id)).fetch(r -> {
      ClassInfo classInfo = r.into(CLASS_INFO).into(ClassInfo.class);
      classInfo.setClassName(r.getValue(PROJECT.NAME));
      classInfo.setOrganization(r.getValue(organizationName));
      return classInfo;
    });
    return list;
  }


  @Override
  public Optional<ClassInfo> findClassByMemberId(String memberId) {
    SelectOnConditionStep<Record> step = classDao.execute(x -> x
            .selectDistinct(Fields.start()
                    .add(CLASS_INFO.ID, PROJECT.NAME, CLASS_INFO.ARRIVE_DATE, CLASS_INFO.RETURN_DATE,
                            CLASS_INFO.STATUS,ORGANIZATION.NAME.as("orgName")).end())
            .from(CLASS_INFO)
            .leftJoin(TRAINEE).on(TRAINEE.CLASS_ID.eq(CLASS_INFO.ID))
            .leftJoin(PROJECT).on(CLASS_INFO.PROJECT_ID.eq(PROJECT.ID))
            .leftJoin(ORGANIZATION).on(PROJECT.ORGANIZATION_ID.eq(ORGANIZATION.ID)));
    Stream<Optional<Condition>> stream = Stream.of(
            Optional.of(ClassInfo.DELETE_FLASE).map(CLASS_INFO.DELETE_FLAG::eq),
            Optional.of(ClassInfo.DELETE_FLASE).map(PROJECT.DELETE_FLAG::eq),
            Optional.of(ClassInfo.DELETE_FLASE).map(TRAINEE.DELETE_FLAG::eq));
    Condition condition = stream.filter(Optional::isPresent).map(Optional::get)
            .reduce((acc, item) -> acc.and(item))
            .orElse(DSL.trueCondition());
    SelectConditionStep<Record> conStep = step.where(condition)
            .and(TRAINEE.MEMBER_ID.eq(memberId)
            .and(CLASS_INFO.STATUS.in(ClassInfo.STATUS_ING).or(CLASS_INFO.STATUS.eq(ClassInfo.STATUS_TRUE).and(CLASS_INFO.RETURN_DATE.le(DateUtil.getTomorrow()))))
            );
    conStep.and(CLASS_INFO.NOTICE.eq(ClassInfo.CLASS_IS_NOTICE));
    return conStep.orderBy(CLASS_INFO.ARRIVE_DATE.desc()).limit(1).fetchOptional(r -> {
      ClassInfo classInfo = r.into(ClassInfo.class);
      classInfo.setClassName(r.getValue(PROJECT.NAME));
      classInfo.setOrganization(r.getValue(ORGANIZATION.NAME.as("orgName")));
      return classInfo;
    });
  }

  @Override
  public List<ClassInfo> findMyTaskClass(String memberId, Optional<Integer> status,
      Optional<String> className) {
    SelectOnConditionStep<Record> step = classDao.execute(x -> x
        .selectDistinct(Fields.start()
            .add(CLASS_INFO.ID, PROJECT.NAME, CLASS_INFO.ARRIVE_DATE, CLASS_INFO.RETURN_DATE,
                CLASS_INFO.STATUS).end())
        .from(CLASS_INFO)
        .leftJoin(TRAINEE).on(TRAINEE.CLASS_ID.eq(CLASS_INFO.ID))
        .leftJoin(PROJECT).on(CLASS_INFO.PROJECT_ID.eq(PROJECT.ID)));
    Stream<Optional<Condition>> stream = Stream.of(className.map(PROJECT.NAME::contains),
        Optional.of(ClassInfo.DELETE_FLASE).map(CLASS_INFO.DELETE_FLAG::eq),
        Optional.of(ClassInfo.DELETE_FLASE).map(PROJECT.DELETE_FLAG::eq),
        Optional.of(ClassInfo.DELETE_FLASE).map(TRAINEE.DELETE_FLAG::eq));
    Condition condition = stream.filter(Optional::isPresent).map(Optional::get)
        .reduce((acc, item) -> acc.and(item))
        .orElse(DSL.trueCondition());
    SelectConditionStep<Record> conStep = step.where(condition).and(TRAINEE.MEMBER_ID.eq(memberId));
    if (status.isPresent()) {
      if (status.get() == ClassInfo.STATUS_NOT_START) {
//                conStep.and(CLASS_INFO.ARRIVE_DATE.greaterThan(date));
        conStep.and(CLASS_INFO.STATUS.eq(1));
      }
      if (status.get() == ClassInfo.STATUS_NOT_FINISH) {
//                conStep.and(CLASS_INFO.ARRIVE_DATE.lessThan(date))
//                        .and(CLASS_INFO.RETURN_DATE.greaterThan(date));
        conStep.and(CLASS_INFO.STATUS.eq(2));
      }
      if (status.get() == ClassInfo.STATUS_TRUE) {
        conStep.and(CLASS_INFO.STATUS.eq(3));
      }
    } else {
//            conStep.and(CLASS_INFO.RETURN_DATE.greaterThan(date));
      conStep.and(CLASS_INFO.STATUS.eq(3));
    }
    conStep.and(CLASS_INFO.NOTICE.eq(ClassInfo.CLASS_IS_NOTICE));
    List<ClassInfo> list = conStep.fetch(r -> {
      ClassInfo classInfo = r.into(ClassInfo.class);
      Project project = r.into(Project.class);
      classInfo.setClassName(project.getName());
//            if (date < classInfo.getArriveDate() || classInfo.getArriveDate() == null) {
//                classInfo.setStatus(ClassInfo.STATUS_NOT_START);
//            } else if (date < classInfo.getReturnDate() || classInfo.getReturnDate() == null) {
//                classInfo.setStatus(ClassInfo.STATUS_NOT_FINISH);
//            }
      Integer value = r.getValue(CLASS_INFO.STATUS);
      if (value != null) {
        if (value == 1) {
          classInfo.setStatus(2);
        } else if (value == 2) {
          classInfo.setStatus(1);
        } else {
          classInfo.setStatus(3);
        }
      }
      return classInfo;
    });
    return list;
  }


  @Override
  public PagedResult<ClassInfo> findActivityClassInfo(Integer page, Integer pageSize,
      Optional<String> name,
      Optional<Integer> status) {
    Field<String> projectName = PROJECT.NAME.as("projectName");

    // 构建查询语句
    SelectOnConditionStep<Record> step = classDao.execute(x -> x
        .selectDistinct(Fields.start()
            .add(CLASS_INFO.ID, CLASS_INFO.ARRIVE_DATE, CLASS_INFO.TRAINEE_NUM,
                CLASS_INFO.RETURN_DATE,
                projectName, CLASS_INFO.STATUS, CLASS_INFO.SUBMIT_NUM)
            .end())
        .from(CLASS_INFO)
        .leftJoin(PROJECT).on(CLASS_INFO.PROJECT_ID.eq(PROJECT.ID))
        .leftJoin(CLASS_SIGNUP_INFO).on(CLASS_SIGNUP_INFO.CLASS_ID.eq(CLASS_INFO.ID)));
    // 构建组合条件
    Stream<Optional<Condition>> conditions = Stream.of(name.map(PROJECT.NAME::contains),
        Optional.of(DELETE_FLASE).map(CLASS_INFO.DELETE_FLAG::eq),
        Optional.of(DELETE_FLASE).map(PROJECT.DELETE_FLAG::eq));
    // 合并条件
    Condition c = conditions.filter(Optional::isPresent).map(Optional::get)
        .reduce((acc, item) -> acc.and(item))
        .orElse(DSL.trueCondition());
    SelectConditionStep<Record> condStep = step.where(c);
    if (status.isPresent()) {
      if (status.get() == STATUS_ING) {
//                condStep.and(CLASS_INFO.ARRIVE_DATE.greaterThan(date));
        condStep.and(CLASS_INFO.STATUS.eq(1));
      }
      if (status.get() == STATUS_FALSE) {
//                condStep.and(CLASS_INFO.ARRIVE_DATE.lessThan(date))
//                        .and(CLASS_INFO.RETURN_DATE.greaterThan(date));
        condStep.and(CLASS_INFO.STATUS.eq(2));
      }
      if (status.get() == STATUS_TRUE) {
//                condStep.and(CLASS_INFO.RETURN_DATE.lessThan(date));
        condStep.and(CLASS_INFO.STATUS.eq(3));
      }
    }
    condStep.and(CLASS_INFO.NOTICE.eq(ClassInfo.CLASS_IS_NOTICE).and(
        CLASS_SIGNUP_INFO.IS_OPEN.eq(com.zxy.product.train.entity.ClassSignupInfo.IS_OPEN_TRUE)));
    // and(GRANT_DETAIL.URI.eq(ClassInfoService.URI)).
    Integer count = classDao.execute(e -> e.fetchCount(condStep));
    if (status.isPresent()) {
      if (status.get() == STATUS_ING) {//未实施
        condStep.orderBy(CLASS_INFO.ARRIVE_DATE.asc());
      } else if (status.get() == STATUS_FALSE) {//实施中
        condStep.orderBy(CLASS_INFO.ARRIVE_DATE.desc());
      } else if (status.get() == STATUS_TRUE) {//已实施
      } else {
        condStep.orderBy(CLASS_INFO.ARRIVE_DATE.desc());
        condStep.orderBy(CLASS_INFO.ARRIVE_DATE.desc());

      }
    } else {
      condStep.orderBy(CLASS_INFO.ARRIVE_DATE.desc());
    }
    // 获取列表
    List<ClassInfo> list = condStep.limit((page - 1) * pageSize, pageSize).fetch(r -> {
      ClassInfo cf = new ClassInfo();
      cf.setId(r.getValue(CLASS_INFO.ID));
      cf.setReturnDate(r.getValue(CLASS_INFO.RETURN_DATE));
      cf.setArriveDate(r.getValue(CLASS_INFO.ARRIVE_DATE));
      cf.setClassName(r.getValue(projectName));
      cf.setTraineeNum(r.getValue(CLASS_INFO.TRAINEE_NUM));//参与人数
      int temporaryStatus = 3;
//            long date = System.currentTimeMillis();
//            if (cf.getArriveDate() == null || date < cf.getArriveDate()) {
//                temporaryStatus = 2;
//            } else if (cf.getReturnDate() == null || date < cf.getReturnDate()) {
//                temporaryStatus = 1;
//            }
      Integer value = r.getValue(CLASS_INFO.STATUS);
      if (value != null) {
        if (value == 1) {
          temporaryStatus = 2;
        } else if (value == 2) {
          temporaryStatus = 1;
        } else {
          temporaryStatus = 3;
        }
      }
      cf.setStatus(temporaryStatus); // 班级状态
      return cf;
    });
    return PagedResult.create(count, list);
  }

  @Override
  public PagedResult<ClassInfo> findMyClass(Integer page, Integer pageSize, List<String> memberIds,
      Optional<Integer> status, Optional<String> name, Optional<Integer> arriveDateOrderBy) {
    Field<String> orgName = ORGANIZATION.NAME.as("orgName");
    SelectOnConditionStep<Record> step = classDao.execute(x -> x
        .select(Fields.start()
            .add(CLASS_INFO.ID, CLASS_INFO.STATUS, CLASS_INFO.ARRIVE_DATE, CLASS_INFO.RETURN_DATE,
                PROJECT.NAME, TRAINEE.AUDIT_STATUS, orgName, PROJECT.ORGANIZATION_ID)
            .add(TRAINEE.TYPE, TRAINEE.MEMBER_ID).add(PROJECT.IS_PARTY_CADRE)
            .end())
        .from(CLASS_INFO)
        .leftJoin(TRAINEE).on(TRAINEE.CLASS_ID.eq(CLASS_INFO.ID))
        .leftJoin(PROJECT).on(CLASS_INFO.PROJECT_ID.eq(PROJECT.ID))
        .leftJoin(ORGANIZATION).on(PROJECT.ORGANIZATION_ID.eq(ORGANIZATION.ID)));
    Optional<Integer> deleteFlag = Optional.of(DELETE_FLASE);
    Stream<Optional<Condition>> stream = Stream.of(name.map(PROJECT.NAME::contains),
        deleteFlag.map(CLASS_INFO.DELETE_FLAG::eq), deleteFlag.map(PROJECT.DELETE_FLAG::eq),
        deleteFlag.map(TRAINEE.DELETE_FLAG::eq));
    Condition condition = stream.filter(Optional::isPresent).map(Optional::get)
        .reduce((acc, item) -> acc.and(item))
        .orElse(DSL.trueCondition());
    SelectConditionStep<Record> conStep = step.where(condition);
    if (status.isPresent()) {
//            if (status.get() == 2) {
//                conStep.and(CLASS_INFO.ARRIVE_DATE.greaterThan(date)).and(TRAINEE.AUDIT_STATUS.eq(AUDIT_AGREE));
//            } else if (status.get() == 1) {
//                conStep.and(CLASS_INFO.ARRIVE_DATE.lessThan(date)).and(CLASS_INFO.RETURN_DATE.greaterThan(date)).and(TRAINEE.AUDIT_STATUS.eq(AUDIT_AGREE));
//            } else if (status.get() == 3) {
//                conStep.and(CLASS_INFO.RETURN_DATE.lessThan(date)).and(TRAINEE.AUDIT_STATUS.eq(AUDIT_AGREE));
//            } else if (status.get() == 4) {
//                conStep.and(TRAINEE.AUDIT_STATUS.eq(Trainee.AUDIT_WAIT));
//            } else if (status.get() == 0) {
//                conStep.and(TRAINEE.AUDIT_STATUS.ne(Trainee.AUDIT_REFUSE).and(TRAINEE.AUDIT_STATUS.ne(Trainee.AUDIT_REFUSE_LIMIT)));
//            }
      if (status.get() == 2) {
        conStep.and(CLASS_INFO.STATUS.eq(1));
      } else if (status.get() == 1) {
        conStep.and(CLASS_INFO.STATUS.eq(2));
      } else if (status.get() == 3) {
        conStep.and(CLASS_INFO.STATUS.eq(3));
      } else if (status.get() == 4) {
        conStep.and(TRAINEE.AUDIT_STATUS.eq(Trainee.AUDIT_WAIT));
      } else if (status.get() == 0) {
        conStep.and(TRAINEE.AUDIT_STATUS.ne(Trainee.AUDIT_REFUSE)
            .and(TRAINEE.AUDIT_STATUS.ne(Trainee.AUDIT_REFUSE_LIMIT)));
      }
    } else {
      conStep.and(TRAINEE.AUDIT_STATUS.ne(Trainee.AUDIT_REFUSE)
          .and(TRAINEE.AUDIT_STATUS.ne(Trainee.AUDIT_REFUSE_LIMIT)));
    }
    conStep.and(TRAINEE.MEMBER_ID.in(memberIds))
        .and(CLASS_INFO.NOTICE.eq(ClassInfo.CLASS_IS_NOTICE)).groupBy(CLASS_INFO.ID);
    if (arriveDateOrderBy.isPresent() && arriveDateOrderBy.get() == 1) {
      conStep.orderBy(CLASS_INFO.ARRIVE_DATE.asc());
    } else {
      conStep.orderBy(CLASS_INFO.ARRIVE_DATE.desc());
    }
    Integer count = classDao.execute(e -> e.fetchCount(conStep));
    List<ClassInfo> list = conStep.limit((page - 1) * pageSize, pageSize).fetch(r -> {
      ClassInfo cf = r.into(ClassInfo.class);
      Trainee t = r.into(Trainee.class);
      cf.setClassName(r.getValue(PROJECT.NAME));
      cf.setOrganization(r.getValue(orgName));
      cf.setOrganizationId(r.getValue(PROJECT.ORGANIZATION_ID));
      cf.setTraineeType(r.getValue(TRAINEE.TYPE));
      cf.setMemberId(r.getValue(TRAINEE.MEMBER_ID));
      cf.setIsPartyCadre(r.getValue(PROJECT.IS_PARTY_CADRE));//是否是党校培训班
      int temporaryStatus = 3;
      Integer value = r.getValue(CLASS_INFO.STATUS);
      if (value != null) {
        if (value == 1) {
          temporaryStatus = 2;
        } else if (value == 2) {
          temporaryStatus = 1;
        } else {
          temporaryStatus = 3;
        }
      } else if (t.getAuditStatus() == Trainee.AUDIT_WAIT) {
        temporaryStatus = 4;
      }
      cf.setReturnDate(cf.getReturnDate() + 86399999);
      cf.setStatus(temporaryStatus); // 班级状态
      return cf;

    });
    List<String> classInfoIds = list.stream().map(x -> x.getId()).collect(Collectors.toList());
    Map<String,ClassInfo> isPartyCadreMap=list.stream().collect(Collectors.toMap(ClassInfo::getId, a -> a,(k1,k2)->k1));
    List<ClassEvaluate> evaluateList = evaluateDao.execute(s -> s.select(Fields.start()
        .add(CLASS_EVALUATE).add(RESEARCH_RECORD.STATUS).add(CLASS_INFO.ARRIVE_DATE).end()).from(CLASS_EVALUATE)
        .innerJoin(RESEARCH_RECORD)
        .on(CLASS_EVALUATE.RESOURCE_ID.eq(RESEARCH_RECORD.RESEARCH_QUESTIONARY_ID)
            .and(RESEARCH_RECORD.MEMBER_ID.in(memberIds)))
            .leftJoin(RESEARCH_QUESTIONARY).on(RESEARCH_QUESTIONARY.ID.eq(RESEARCH_RECORD.RESEARCH_QUESTIONARY_ID))
         .leftJoin(CLASS_INFO).on(CLASS_INFO.ID.eq(CLASS_EVALUATE.CLASS_ID))
        .where(CLASS_EVALUATE.TYPE.in(ClassEvaluate.TYPE_EVA_STU, ClassEvaluate.TYPE_EVA_STU_NEW)
            .and(CLASS_EVALUATE.CLASS_ID.in(classInfoIds))
            .and(CLASS_EVALUATE.DELETE_FLAG.eq(0))
                .and(RESEARCH_QUESTIONARY.IS_ENSEMBLE.eq(1))
        ).fetch(l -> {
          ClassEvaluate classEvaluate = l.into(ClassEvaluate.class);
          //wqw
          //如果类型4的，做兼容，判断当前报道时间是否在20190512后，如果是类型改为8
          if(classEvaluate.getType()==ClassEvaluate.TYPE_EVA_STU){
            Long reportTime=l.getValue(CLASS_INFO.ARRIVE_DATE);
            if(reportTime!=null){
              String dateStr=DateUtil.dateLongToString(reportTime,"yyyyMMdd");
              Integer dateInteger=Integer.valueOf(dateStr);
              if(dateInteger>=ClassEvaluate.EVA_STU_DATE){
                classEvaluate.setType(ClassEvaluate.TYPE_EVA_STU_NEW);
              }
            }
          }
          classEvaluate.setEvaluateStatus(l.getValue(RESEARCH_RECORD.STATUS));
          String classId=l.getValue(CLASS_EVALUATE.CLASS_ID);
          classEvaluate.setIsPartyCadre(isPartyCadreMap.get(classId).getIsPartyCadre());//添加是否是党校培训班字段
          if(classEvaluate.getIsPartyCadre()==1){
            boolean flag=existMemberSubmitStatus(classEvaluate.getClassId(),memberIds);
            if(flag){
              classEvaluate.setEvaluateStatus(1);
            }else{
              classEvaluate.setEvaluateStatus(0);
            }
            classEvaluate.setResourceName("满意度问卷(学员)");
          }
          return classEvaluate;
        }));
    List<Bus> busList = busDao.execute(b -> b.select(Fields.start().add(BUS).end()).from(BUS))
        .where(BUS.DELETE_FLAG.eq(0).and(BUS.CLASS_ID.in(classInfoIds))).fetch(u -> {
          Bus bus = u.into(Bus.class);
          return bus;
        });
    List<ClassEvaluate> classEvaluateList = evaluateDao.execute(x -> x
        .select(Fields.start().add(CLASS_EVALUATE).add(CLASS_BUSINESS_PROGRESS.FINISH_STATUS)
            .add(CLASS_BUSINESS_PROGRESS.SCORE).end())
        .from(CLASS_EVALUATE)
        .leftJoin(CLASS_BUSINESS_PROGRESS)
        .on(CLASS_BUSINESS_PROGRESS.CLASS_BUSINESS_ID.eq(CLASS_EVALUATE.ID)
            .and(CLASS_BUSINESS_PROGRESS.CLASS_ID.in(classInfoIds))
            .and(CLASS_BUSINESS_PROGRESS.MEMBER_ID.in(memberIds)))
        .where(CLASS_EVALUATE.DELETE_FLAG.eq(0).and(CLASS_EVALUATE.CLASS_ID.in(classInfoIds)))
        .and(CLASS_EVALUATE.TYPE.eq(1)))
        .groupBy(CLASS_EVALUATE.CLASS_ID)
        .fetch(e -> {
          ClassEvaluate c = e.into(ClassEvaluate.class);
          c.setEvaluateStatus(e.getValue(CLASS_BUSINESS_PROGRESS.FINISH_STATUS));
          c.setScore(e.getValue(CLASS_BUSINESS_PROGRESS.SCORE));
          return c;
        });
    evaluateList.addAll(classEvaluateList);
    Map<String, List<ClassEvaluate>> evaluateMap = evaluateList.stream()
        .collect(Collectors.groupingBy(ClassEvaluate::getClassId));
    Map<String, List<Bus>> busMap = busList.stream()
        .collect(Collectors.groupingBy(Bus::getClassId));
    list.stream().forEach(x -> {
      x.setClassEvaluate(evaluateMap.get(x.getId()));
      x.setBusList(busMap.get(x.getId()));
    });
    return PagedResult.create(count, list);
  }

  @Override
  public ClassInfo updateTraineeNum(String id, Integer trainee_num, boolean flag, boolean flag1,
      boolean flag2) {
    ClassInfo classInfo = classDao.get(id);
    classInfo.setTraineeNum(trainee_num);
//        if(flag){
//            classInfo.setFourDegreesSubmitNum(classInfo.getFourDegreesSubmitNum()-1);
//        }
//        if(flag1){
//            classInfo.setAbilitySubmitNum(classInfo.getAbilitySubmitNum()-1);
//        }
//        if(flag2){
//            classInfo.setSuperiorLeadershipSubmitNum(classInfo.getSuperiorLeadershipSubmitNum()-1);
//        }
    classDao.update(classInfo);
    return classInfo;
  }

  @Override
  public ClassInfo updateSubmitNum(String id, Integer submit_num) {
    ClassInfo classInfo = classDao.get(id);
    classInfo.setSubmitNum(submit_num);
    classDao.update(classInfo);
    return classInfo;
  }

  @Override
  public Optional<ClassInfo> getClassInfoByExamId(String examId) {

    return classDao.execute(x -> x.select(PROJECT.ORGANIZATION_ID, CLASS_INFO.ID, PROJECT.NAME)
        .from(CLASS_INFO)
        .leftJoin(CLASS_EVALUATE).on(CLASS_EVALUATE.CLASS_ID.eq(CLASS_INFO.ID))
        .leftJoin(PROJECT).on(PROJECT.ID.eq(CLASS_INFO.PROJECT_ID))
        .where(CLASS_EVALUATE.RESOURCE_ID.eq(examId)
            .and(PROJECT.DELETE_FLAG.eq(Project.DELETE_FLASE))
            .and(CLASS_INFO.DELETE_FLAG.eq(ClassInfo.DELETE_FLASE))
            .and(CLASS_EVALUATE.DELETE_FLAG.eq(ClassEvaluate.DELETE_FALSE)))
        .fetchOptional(r -> {
          ClassInfo classInfo = r.into(ClassInfo.class);
          classInfo.setOrganizationId(r.getValue(PROJECT.ORGANIZATION_ID));//组织Id
          classInfo.setClassName(r.getValue(PROJECT.NAME));//班级名称
          return classInfo;
        }));
  }

  @Override
  public ClassInfo insert(String projectId, Optional<String> classTeacherPhone,
      Optional<String> classTeacher,
      Optional<Long> arriveDate, Optional<Long> returnDate, Optional<Integer> isOutSide,
      Optional<String> surveyType, Optional<String> target, Optional<String> level,
      Optional<String> studentType,
      Optional<String> simpleType, Optional<Integer> isPlan, Optional<Integer> implementationYear,
      Optional<Integer> implementationMonth, Optional<Integer> status,
      Optional<String> createMemberId,
      Optional<Integer> resourceStatus, Double totalitySatisfied, Double courseSatisfied,
      Optional<Integer> projectSource, String organizationId, Integer falg, Integer qstatus) {
    ClassInfo classInfo = new ClassInfo();
    classInfo.forInsert();
    classInfo.setProjectId(projectId);
    classTeacherPhone.ifPresent(classInfo::setClassTeacherPhone);
    classTeacher.ifPresent(classInfo::setClassTeacher);
    arriveDate.ifPresent(classInfo::setArriveDate);
    returnDate.ifPresent(classInfo::setReturnDate);
    isOutSide.ifPresent(classInfo::setIsOutside);
    surveyType.ifPresent(classInfo::setSurveyType);
    target.ifPresent(classInfo::setTarget);
    level.ifPresent(classInfo::setClassInfoType);
    studentType.ifPresent(classInfo::setStudentType);
    simpleType.ifPresent(classInfo::setSimpleType);
    isPlan.ifPresent(classInfo::setIsPlan);
    status.ifPresent(classInfo::setStatus);
    if (status.isPresent()) {
      if (status.get().equals(ClassInfo.STATUS_FALSE)) {
        classInfo.setSort(ClassInfo.SORT_ING);
      } else if (status.get().equals(ClassInfo.STATUS_ING)) {
        classInfo.setSort(ClassInfo.SORT_FALSE);
      } else {
        classInfo.setSort(ClassInfo.SORT_TRUE);
      }
    }
    implementationYear.ifPresent(classInfo::setImplementationYear);
    implementationMonth.ifPresent(classInfo::setImplementationMonth);
    createMemberId.ifPresent(classInfo::setCreateMember);
    classInfo.setDeleteFlag(ClassInfo.DELETE_FLASE);
    resourceStatus.ifPresent(classInfo::setResourceStatus);
    classInfo.setTotalitySatisfied(totalitySatisfied);
    classInfo.setCourseSatisfied(courseSatisfied);
    classInfo.setOrganizationId(organizationId);
    projectSource.ifPresent(classInfo::setProjectSource);
    classInfo.setQuestionnaireStatus(qstatus);
    ClassInfo c = classDao.insert(classInfo);
    sender.send(MessageTypeContent.TRAIN_CLASS_INSERT, MessageHeaderContent.ID, classInfo.getId());
    return c;
  }

  @Override
  public List<ClassInfo> findBasicClassInfoByIds(List<String> ids) {
    SelectConditionStep<Record> step = classDao.execute(x -> x.select(Fields.start()
        .add(CLASS_INFO.ID, PROJECT.NAME, CLASS_INFO.ARRIVE_DATE, CLASS_INFO.RETURN_DATE)
        .add(CLASS_INFO.TRAINEE_NUM)
        .end())
        .from(CLASS_INFO)
        .leftJoin(PROJECT).on(PROJECT.ID.eq(CLASS_INFO.PROJECT_ID))
        .leftJoin(CLASS_SIGNUP_INFO).on(CLASS_INFO.ID.eq(CLASS_SIGNUP_INFO.CLASS_ID))
        .where(CLASS_INFO.ID.in(ids).and(PROJECT.DELETE_FLAG.eq(Project.DELETE_FLASE))
            .and(CLASS_INFO.DELETE_FLAG.eq(ClassInfo.DELETE_FLASE))
            .and(CLASS_INFO.NOTICE.eq(ClassInfo.CLASS_IS_NOTICE))
            .and(CLASS_SIGNUP_INFO.IS_OPEN.eq(ClassSignupInfo.IS_OPEN_TRUE))));
    List<ClassInfo> list = step.fetch(r -> {
      ClassInfo info = r.into(ClassInfo.class);
      info.setClassName(r.getValue(PROJECT.NAME));//班级名称
      return info;
    });
    return list;
  }

  @Override
  public List<ClassInfo> findClassInfoByIds(List<String> ids) {
    return findClassInfoByDate(ids,Optional.empty(),Optional.empty());
  }


  @Override
  public List<ClassInfo> findClassInfoByDate(List<String> ids,Optional<Long> arriveDateBegin,Optional<Long> arriveDateEnd) {
    com.zxy.product.train.jooq.tables.ConfigurationValue configurationValue = CONFIGURATION_VALUE
            .as("configurationValue");
    Optional<List<String>> optIds = Optional.of(ids);
    Stream<Optional<Condition>> stream = Stream.of(arriveDateBegin.map(CLASS_INFO.ARRIVE_DATE::ge),
            arriveDateEnd.map(CLASS_INFO.ARRIVE_DATE::le),optIds.map(CLASS_INFO.ID::in));
    Condition condition = stream.filter(Optional::isPresent).map(Optional::get)
            .reduce((acc, item) -> acc.and(item))
            .orElse(DSL.trueCondition());
    SelectConditionStep<Record> step = classDao.execute(x -> x.select(Fields.start()
            .add(CLASS_INFO.ID, PROJECT.NAME, PROJECT.AMOUNT, CLASS_INFO.ARRIVE_DATE,
                    CLASS_INFO.RETURN_DATE, CLASS_INFO.TRAINEE_NUM,CLASS_INFO.STATUS,CLASS_INFO.DELETE_FLAG)
            .add(ORGANIZATION.NAME, CLASS_INFO.TRAINEE_NUM, PROJECT.ORGANIZATION_ID, PROJECT.ADDRESS)
            .add(CLASS_DETAIL.BANNER_ID, CLASS_DETAIL.PATH, CLASS_DETAIL.COVER_PATH)
            .add(configurationValue.NAME)
            .add(PROJECT.CODE)
            .add(CLASS_INFO.IMPLEMENTATION_YEAR, CLASS_INFO.IMPLEMENTATION_MONTH)
                            .add(CLASS_INFO.CREATE_TIME,CLASS_INFO.CREATE_MEMBER)
            .end())
            .from(CLASS_INFO)
            .innerJoin(PROJECT).on(PROJECT.ID.eq(CLASS_INFO.PROJECT_ID))
            .innerJoin(ORGANIZATION).on(ORGANIZATION.ID.eq(PROJECT.ORGANIZATION_ID)))
            .leftJoin(CLASS_DETAIL).on(CLASS_DETAIL.CLASS_ID.eq(CLASS_INFO.ID))
            .leftJoin(configurationValue).on(configurationValue.ID.eq(PROJECT.TYPE_ID))
            .where(condition.and(PROJECT.DELETE_FLAG.eq(Project.DELETE_FLASE))
                    .and(CLASS_INFO.DELETE_FLAG.eq(ClassInfo.DELETE_FLASE)));
    List<ClassInfo> list = step.fetch(r -> {
      ClassInfo info = r.into(CLASS_INFO).into(ClassInfo.class);
      info.setClassName(r.getValue(PROJECT.NAME));//班级名称
      info.setAmount(r.getValue(PROJECT.AMOUNT));//计划人数
      info.setOrganization(r.getValue(ORGANIZATION.NAME));//需求方名称
      info.setOrganizationId(r.getValue(PROJECT.ORGANIZATION_ID));//需求方id
      info.setBannerId(r.getValue(CLASS_DETAIL.BANNER_ID));
      info.setType(r.getValue(configurationValue.NAME));
      info.setPath(r.getValue(CLASS_DETAIL.PATH));
      info.setAddress(r.getValue(PROJECT.ADDRESS));
      info.setTraineeNum(r.getValue(CLASS_INFO.TRAINEE_NUM));
      info.setCoverPath(r.getValue(CLASS_DETAIL.COVER_PATH));
      info.setCode(r.getValue(PROJECT.CODE));
      info.setDeleteFlag(r.getValue(CLASS_INFO.DELETE_FLAG));
      info.setStatus(r.getValue(CLASS_INFO.STATUS));
      return info;
    });
    return list;
  }

  @Override
  public Optional<ClassInfo> findClassInfoOptionalById(String id) {
    SelectConditionStep<Record> where = classDao.execute(x -> x.select(Fields.start()
        .add(CLASS_INFO.ID, PROJECT.NAME, CLASS_INFO.ARRIVE_DATE, CLASS_INFO.RETURN_DATE)
        .add(ORGANIZATION.NAME, CLASS_INFO.TRAINEE_NUM, PROJECT.ORGANIZATION_ID,
            CLASS_SIGNUP_INFO.IS_OPEN, CLASS_INFO.STATUS)
        .end()).from(CLASS_INFO)
        .leftJoin(PROJECT).on(PROJECT.ID.eq(CLASS_INFO.PROJECT_ID))
        .leftJoin(ORGANIZATION).on(ORGANIZATION.ID.eq(PROJECT.ORGANIZATION_ID)))
        .leftJoin(CLASS_SIGNUP_INFO).on(CLASS_INFO.ID.eq(CLASS_SIGNUP_INFO.CLASS_ID))
        .where(CLASS_INFO.ID.eq(id)
            .and(CLASS_INFO.DELETE_FLAG.eq(CLASS_INFO.DELETE_FLAG))
            .and(PROJECT.DELETE_FLAG.eq(Project.DELETE_FLASE))
            .and(CLASS_INFO.NOTICE.eq(ClassInfo.CLASS_IS_NOTICE)));
    Optional<ClassInfo> info = where.fetchOptional(r -> {
      ClassInfo classInfo = r.into(ClassInfo.class);
//            long nowTime = System.currentTimeMillis();
//            Integer status = ClassInfo.STATUS_TRUE;//已结束
//            if (null == classInfo.getArriveDate() || nowTime < classInfo.getArriveDate()) {
//                status = ClassInfo.STATUS_FALSE;//未开始
//            } else if (null == classInfo.getReturnDate() || (nowTime >= classInfo.getArriveDate() && nowTime <= classInfo.getReturnDate())) {
//                status = ClassInfo.STATUS_ING;//报名中
//            }
      classInfo.setClassName(r.getValue(PROJECT.NAME));//班级名称
      classInfo.setOrganization(r.getValue(ORGANIZATION.NAME));//需求方名称
      classInfo.setOrganizationId(r.getValue(PROJECT.ORGANIZATION_ID));//需求方id
      classInfo.setStatus(r.getValue(CLASS_INFO.STATUS));
      classInfo.setIsOpen(r.getValue(CLASS_SIGNUP_INFO.IS_OPEN));
      return classInfo;
    });
    return info;
  }

  @Override
  public PagedResult<ClassInfo> findQuestionare(int page, int pageSize, Optional<String> name,
      Optional<String> MIScode, Optional<String> orgName, Optional<Integer> status,
      Optional<Long> reportBegin,
      Optional<Long> reportEnd, Optional<Long> returnBegin, Optional<Long> returnEnd,
      Optional<Integer> implementation_year, Optional<Integer> implementation_month,
      List<String> organizationIds) {
    List<String> memberIds = new ArrayList<>();
    List<String> orgIds = new ArrayList<>();
//        long date = System.currentTimeMillis();
    List<ClassInfo> basic = classDao.execute(d -> d
        .select(CLASS_EVALUATE.ID.count(), CLASS_INFO.ID, PROJECT.CODE, PROJECT.NAME,
            PROJECT.CONTACT_PHONE, PROJECT.ORGANIZATION_ID, PROJECT.CONTACT_MEMBER_ID)
        .from(CLASS_INFO)
        .innerJoin(PROJECT).on(PROJECT.ID.eq(CLASS_INFO.PROJECT_ID))
        .leftJoin(CLASS_EVALUATE).on(CLASS_INFO.ID.eq(CLASS_EVALUATE.CLASS_ID))
        .and(CLASS_EVALUATE.TYPE.eq(DSL.when(CLASS_INFO.ARRIVE_DATE.lt(ClassInfo.SATISFACTION_TIME),
            ResearchQuestionary.TYPE_SATISFACTION_QUESTIONARY)
            .otherwise(ResearchQuestionary.TYPE_NEW_SATISFACTION_QUESTIONARY)))
        .where(CLASS_INFO.DELETE_FLAG.eq(ClassInfo.DELETE_FLASE))
        .and(name.map(PROJECT.NAME::contains).orElse(DSL.trueCondition()))
        .and(MIScode.map(PROJECT.CODE::contains).orElse(DSL.trueCondition()))
        .and(implementation_month.map(CLASS_INFO.IMPLEMENTATION_MONTH::eq)
            .orElse(DSL.trueCondition()))
        .and(
            implementation_year.map(CLASS_INFO.IMPLEMENTATION_YEAR::eq).orElse(DSL.trueCondition()))
        .and(reportBegin.map(CLASS_INFO.ARRIVE_DATE::ge).orElse(DSL.trueCondition()))
        .and(reportEnd.map(CLASS_INFO.ARRIVE_DATE::le).orElse(DSL.trueCondition()))
        .and(returnBegin.map(CLASS_INFO.RETURN_DATE::ge).orElse(DSL.trueCondition()))
        .and(returnEnd.map(CLASS_INFO.RETURN_DATE::le).orElse(DSL.trueCondition()))
        .and(PROJECT.ORGANIZATION_ID.in(organizationIds))
//        .and(CLASS_INFO.ARRIVE_DATE.le(date))
//                .and(CLASS_INFO.NOTICE.eq(ClassInfo.CLASS_IS_NOTICE))
        .and(PROJECT.STATUS.eq(3))
        .and(status.map(CLASS_INFO.STATUS::eq).orElse(CLASS_INFO.STATUS.ne(ClassInfo.STATUS_FALSE)))
        .groupBy(CLASS_INFO.ID)
        .having(CLASS_EVALUATE.ID.count().gt(0))
        .orderBy(CLASS_INFO.ARRIVE_DATE.desc(), CLASS_INFO.CREATE_TIME.desc())
        .limit((page - 1) * pageSize, pageSize))
        .fetch(r -> {
          ClassInfo c = r.into(ClassInfo.class);
          Project p = r.into(Project.class);
          c.setProject(p);
          memberIds.add(r.getValue(PROJECT.CONTACT_MEMBER_ID));
          orgIds.add(r.getValue(PROJECT.ORGANIZATION_ID));
          return c;
        });
    Map<String, Project> projectMap = basic.stream()
        .collect(Collectors.toMap(ClassInfo::getId, ClassInfo::getProject));
    List<String> ids = basic.stream().map(ClassInfo::getId).collect(Collectors.toList());
    // 构建查询语句
//        Long date = System.currentTimeMillis();
    //存储人员id
    List<ClassInfo> list = classDao.execute(d -> d.select(Fields.start()
        .add(CLASS_INFO.ID, CLASS_INFO.PROJECT_ID, CLASS_INFO.ARRIVE_DATE, CLASS_INFO.TRAINEE_NUM,
            CLASS_INFO.SUBMIT_NUM)
        .add(CLASS_INFO.IMPLEMENTATION_MONTH, CLASS_INFO.IMPLEMENTATION_YEAR,
            CLASS_INFO.RETURN_DATE, CLASS_INFO.STATUS)
        .end())
        .from(CLASS_INFO))
        .where(CLASS_INFO.ID.in(ids))
        .fetch(r -> r.into(CLASS_INFO).into(ClassInfo.class));

    Map<String, Member> map = memberDao.execute(x -> x.select(MEMBER.ID, MEMBER.FULL_NAME)
        .from(MEMBER).where(MEMBER.ID.in(memberIds))).fetch(r -> r.into(Member.class)).stream()
        .collect(Collectors.toMap(Member::getId, o -> o));
    Map<String, Organization> orgMap = orgDao.execute(
        x -> x.select(ORGANIZATION.ID, ORGANIZATION.NAME).from(ORGANIZATION)
            .where(ORGANIZATION.ID.in(orgIds)))
        .fetch(r -> r.into(Organization.class)).stream()
        .collect(Collectors.toMap(Organization::getId, r -> r));

    Integer count = classDao.execute(d -> d.select(CLASS_INFO.ID.countDistinct())
            .from(CLASS_INFO)
            .innerJoin(PROJECT).on(PROJECT.ID.eq(CLASS_INFO.PROJECT_ID))
            .leftJoin(CLASS_EVALUATE).on(CLASS_INFO.ID.eq(CLASS_EVALUATE.CLASS_ID))
            .and(CLASS_EVALUATE.TYPE.eq(DSL.when(CLASS_INFO.ARRIVE_DATE.lt(ClassInfo.SATISFACTION_TIME),
                ResearchQuestionary.TYPE_SATISFACTION_QUESTIONARY)
                .otherwise(ResearchQuestionary.TYPE_NEW_SATISFACTION_QUESTIONARY)))
            .where(CLASS_INFO.DELETE_FLAG.eq(ClassInfo.DELETE_FLASE))
            .and(name.map(PROJECT.NAME::contains).orElse(DSL.trueCondition()))
            .and(MIScode.map(PROJECT.CODE::contains).orElse(DSL.trueCondition()))
            .and(implementation_month.map(CLASS_INFO.IMPLEMENTATION_MONTH::eq)
                .orElse(DSL.trueCondition()))
            .and(
                implementation_year.map(CLASS_INFO.IMPLEMENTATION_YEAR::eq).orElse(DSL.trueCondition()))
            .and(reportBegin.map(CLASS_INFO.ARRIVE_DATE::ge).orElse(DSL.trueCondition()))
            .and(reportEnd.map(CLASS_INFO.ARRIVE_DATE::le).orElse(DSL.trueCondition()))
            .and(returnBegin.map(CLASS_INFO.RETURN_DATE::ge).orElse(DSL.trueCondition()))
            .and(returnEnd.map(CLASS_INFO.RETURN_DATE::le).orElse(DSL.trueCondition()))
            .and(PROJECT.ORGANIZATION_ID.in(organizationIds))
//                    .and(CLASS_INFO.ARRIVE_DATE.le(date))
//                .and(CLASS_INFO.NOTICE.eq(ClassInfo.CLASS_IS_NOTICE))
            .and(PROJECT.STATUS.eq(3))
            .and(status.map(CLASS_INFO.STATUS::eq).orElse(CLASS_INFO.STATUS.ne(ClassInfo.STATUS_FALSE)))
            .and(CLASS_EVALUATE.ID.isNotNull())
    )
        .fetchOne(CLASS_INFO.ID.countDistinct());

    return PagedResult.create(count, list.stream().map(m -> {
      if (projectMap.get(m.getId()) != null) {
        m.setClassName(projectMap.get(m.getId()).getName());
        m.setCode(projectMap.get(m.getId()).getCode());
        m.setContactPhone(projectMap.get(m.getId()).getContactPhone());
        m.setContactPeople(projectMap.get(m.getId()).getContactMemberId());
        m.setOrganizationId(projectMap.get(m.getId()).getOrganizationId());
      }
      if (map.get(m.getContactPeople()) != null) {
        m.setContactPeople(map.get(m.getContactPeople()).getFullName());
      }
      if (orgMap.get(m.getOrganizationId()) != null) {
        m.setOrganization(orgMap.get(m.getOrganizationId()).getName());
      }
      return m;
    }).collect(Collectors.toList()));
  }

  @Override
  public void updateOrganizationIdByProjectId(String projectId, String organizationId,
      Integer findSource) {
    classDao.execute(x -> x.update(CLASS_INFO).set(CLASS_INFO.ORGANIZATION_ID, organizationId)
        .set(CLASS_INFO.PROJECT_SOURCE, findSource)
        .where(CLASS_INFO.PROJECT_ID.eq(projectId))).execute();
  }

  @Override
  public PagedResult<ClassInfo> findByGroup(int page, int pageSize, Optional<String> name,
      Optional<String> orgName, List<String> organizationIds) {
    //存储人员id
    List<String> memberIds = new ArrayList<>();
    List<String> orgIds = new ArrayList<>();
    List<ClassInfo> list = classDao.execute(d -> {
      Table<Record1<String>> basic = (d.select(CLASS_INFO.ID)
          .from(CLASS_INFO)
          .innerJoin(PROJECT).on(PROJECT.ID.eq(CLASS_INFO.PROJECT_ID))
          .where(CLASS_INFO.DELETE_FLAG.eq(ClassInfo.DELETE_FLASE))
          .and(name.map(PROJECT.NAME::contains).orElse(DSL.trueCondition()))
          .and(PROJECT.ORGANIZATION_ID.in(organizationIds))
          .and(CLASS_INFO.DELETE_FLAG.eq(ClassInfo.DELETE_FLASE))
          .and(PROJECT.STATUS.eq(3)).and(CLASS_INFO.GROUP_ID.isNull())
          .orderBy(CLASS_INFO.ARRIVE_DATE.desc(), CLASS_INFO.CREATE_TIME.desc())
          .limit((page - 1) * pageSize, pageSize)
      ).asTable("b");
      return d.select(Fields.start()
          .add(CLASS_INFO.ID, CLASS_INFO.PROJECT_ID)
          .add(PROJECT.CODE, PROJECT.NAME, PROJECT.ORGANIZATION_ID)
          .end())
          .from(CLASS_INFO)
          .innerJoin(basic).on(basic.field(CLASS_INFO.ID).eq(CLASS_INFO.ID))
          .innerJoin(PROJECT).on(PROJECT.ID.eq(CLASS_INFO.PROJECT_ID))
          .fetch(r -> {
            ClassInfo cf = r.into(ClassInfo.class);
            cf.setClassName(r.getValue(PROJECT.NAME));
            cf.setCode(r.getValue(PROJECT.CODE));
            cf.setOrganizationId(r.getValue(PROJECT.ORGANIZATION_ID));
            orgIds.add(r.getValue(PROJECT.ORGANIZATION_ID));
            return cf;
          });
    });

    Map<String, Member> map = memberDao.execute(x -> x.select(MEMBER.ID, MEMBER.FULL_NAME)
        .from(MEMBER).where(MEMBER.ID.in(memberIds))).fetch(r -> r.into(Member.class)).stream()
        .collect(Collectors.toMap(Member::getId, o -> o));
    Map<String, Organization> orgMap = orgDao.execute(
        x -> x.select(ORGANIZATION.ID, ORGANIZATION.NAME).from(ORGANIZATION)
            .where(ORGANIZATION.ID.in(orgIds)))
        .fetch(r -> r.into(Organization.class)).stream()
        .collect(Collectors.toMap(Organization::getId, r -> r));

    Integer count = classDao.execute(d -> d.select(CLASS_INFO.ID.count())
        .from(CLASS_INFO)
        .innerJoin(PROJECT).on(PROJECT.ID.eq(CLASS_INFO.PROJECT_ID))
        .where(CLASS_INFO.DELETE_FLAG.eq(ClassInfo.DELETE_FLASE))
        .and(name.map(PROJECT.NAME::contains).orElse(DSL.trueCondition()))
        .and(PROJECT.ORGANIZATION_ID.in(organizationIds))
        .and(CLASS_INFO.DELETE_FLAG.eq(ClassInfo.DELETE_FLASE))
        .and(PROJECT.STATUS.eq(3)).and(CLASS_INFO.GROUP_ID.isNull()))
        .fetchOne(CLASS_INFO.ID.count());
    return PagedResult.create(count, list.stream().map(m -> {
      if (orgMap.get(m.getOrganizationId()) != null) {
        m.setOrganization(orgMap.get(m.getOrganizationId()).getName());
      }
      return m;
    }).collect(Collectors.toList()));
  }

  @Override
  public ClassInfo findClassIdByProjectId(String projectId) {
    Optional<ClassInfo> classInfo = classDao.fetchOne(CLASS_INFO.PROJECT_ID.eq(projectId));
    return classInfo.isPresent() ? classInfo.get() : null;
  }

  @Override
  public List<ClassInfo> findByUserIds(List<String> ids, String memberId) {
    SelectConditionStep<Record> step = classDao.execute(x -> x.select(Fields.start()
        .add(CLASS_INFO.ID, PROJECT.NAME, PROJECT.AMOUNT, CLASS_INFO.ARRIVE_DATE,
            CLASS_INFO.RETURN_DATE, CLASS_INFO.TRAINEE_NUM)
        .add(ORGANIZATION.NAME, CLASS_INFO.TRAINEE_NUM, PROJECT.ORGANIZATION_ID, PROJECT.ADDRESS)
        .add(CLASS_DETAIL.BANNER_ID, CLASS_DETAIL.PATH)
        .end())
        .from(CLASS_INFO)
        .innerJoin(PROJECT).on(PROJECT.ID.eq(CLASS_INFO.PROJECT_ID))
        .innerJoin(ORGANIZATION).on(ORGANIZATION.ID.eq(PROJECT.ORGANIZATION_ID)))
        .leftJoin(CLASS_DETAIL).on(CLASS_DETAIL.CLASS_ID.eq(CLASS_INFO.ID))
        .leftJoin(CLASS_SIGNUP_INFO).on(CLASS_SIGNUP_INFO.CLASS_ID.eq(CLASS_INFO.ID))
        .where(CLASS_INFO.ID.in(ids).and(PROJECT.DELETE_FLAG.eq(Project.DELETE_FLASE))
            .and(CLASS_INFO.DELETE_FLAG.eq(ClassInfo.DELETE_FLASE))
            .and(CLASS_INFO.NOTICE.eq(ClassInfo.CLASS_IS_NOTICE).and(CLASS_SIGNUP_INFO.IS_OPEN
                .eq(com.zxy.product.train.entity.ClassSignupInfo.IS_OPEN_TRUE))));
    List<ClassInfo> list = step.fetch(r -> {
      ClassInfo info = r.into(CLASS_INFO).into(ClassInfo.class);
      info.setClassName(r.getValue(PROJECT.NAME));//班级名称
      info.setAmount(r.getValue(PROJECT.AMOUNT));//计划人数
      info.setOrganization(r.getValue(ORGANIZATION.NAME));//需求方名称
      info.setOrganizationId(r.getValue(PROJECT.ORGANIZATION_ID));//需求方id
      info.setBannerId(r.getValue(CLASS_DETAIL.BANNER_ID));
      info.setPath(r.getValue(CLASS_DETAIL.PATH));
      info.setAddress(r.getValue(PROJECT.ADDRESS));
      info.setTraineeNum(r.getValue(CLASS_INFO.TRAINEE_NUM));
      return info;
    });
    return list;
  }

  /**
   * 通过班级id返回班级名称
   *
   * @param classId
   * @return
   */
  @Override
  public ClassInfo findClassIdByProjectName(String classId) {
    ClassInfo classInfo = classDao.execute(
        x -> x.select(Fields.start().add(PROJECT.NAME).end()).from(CLASS_INFO).leftJoin(PROJECT)
            .on(PROJECT.ID.eq(CLASS_INFO.PROJECT_ID)).where(CLASS_INFO.ID.eq(classId))
            .fetchOne(r -> {
              ClassInfo ci = new ClassInfo();
              ci.setName(r.getValue(PROJECT.NAME));
              return ci;
            }));
    return classInfo;
  }

  @Override
  public ClassInfo updateManage(String id, Optional<String> classTeacher, Optional<String> MemberId,
      Optional<String> restRoom, Optional<String> classRoom, Optional<String> diningRoom,
      Optional<String> coverId, Optional<Integer> haveProvinceLeader,
      Optional<Integer> haveMinister,
      Optional<Integer> needGroupPhoto, Optional<Long> photoTime, Optional<Integer> needVideo,
      Optional<String> videoRequirement, Optional<Integer> needMakeCourse,
      Optional<String> courseVideoRequirement, Optional<Integer> needNet,
      Optional<String> tableType,
      Optional<String> otherRequirement, Optional<String> bannerId, Optional<Integer> confirm,
      Optional<String> teacherPhone, Optional<String> classInfoType, Optional<String> path,
      Optional<String> restaurantsIds,
      Optional<String> guestroomsIds, Optional<Integer> role, Optional<String> coverPath,
      Optional<Integer> falg) {
    // TODO Auto-generated method stub
    ClassInfo classInfo = classDao.get(id);
    confirm.ifPresent(classInfo::setConfirm);
    if (classTeacher.isPresent() && !classTeacher.get().equals(classInfo.getClassTeacher())) {
      classstaffClassService.delTeacher(id, classInfo.getClassTeacher());
      int num = classstaffClassService.countByClass(id);
      if (num < 10) {
        classTeacher.ifPresent(x -> {
          classInfo.setClassTeacher(x);
          classstaffClassService.insertMasterTeacher(id, classTeacher.get());
        });
      }
    }
    teacherPhone.ifPresent(classInfo::setClassTeacherPhone);
    classInfoType.ifPresent(classInfo::setClassInfoType);
    classDao.update(classInfo);

    // 更新培训资源
	        /*Optional<ClassResource> classResource = resourceService.findByClassId(id);
	        ClassResource cr = null;
	        if (classResource.isPresent()) {
	            cr = classResource.get();
	            cr.setRestRoom(restRoom.orElse(null));
	            cr.setClassroom(classRoom.orElse(null));
	            cr.setDiningRoom(diningRoom.orElse(null));
	            classResourceDao.update(cr);
	        } else {
	            resourceService.insert(id, restRoom, diningRoom, classRoom, Optional.empty());
	        }*/
    if (!role.isPresent()) {
      classResourceDao.delete(CLASS_RESOURCE.CLASS_ID.eq(id));
      if (classRoom.isPresent()) {
        resourceService.insert(id, Optional.empty(), Optional.empty(), classRoom, Optional.empty());
        signDao.execute(x -> x
            .update(SIGN)
            .set(SIGN.PLACE, classRoom.get())
            .where(SIGN.CLASS_ID.eq(id))).execute();
        classOfflineCourseDao.execute(x -> x
            .update(CLASS_OFFLINE_COURSE)
            .set(CLASS_OFFLINE_COURSE.CLASSROOM_ID, classRoom.get())
            .where(CLASS_OFFLINE_COURSE.CLASS_ID.eq(id))).execute();
      }
      if (restaurantsIds.isPresent()) {
        String[] rIds = restaurantsIds.get().split(",");
        for (String cr : rIds) {
          resourceService.insert(id, Optional.empty(), Optional.ofNullable(cr), Optional.empty(),
              Optional.empty());
        }
      }
      if (guestroomsIds.isPresent()) {
        String[] grIds = guestroomsIds.get().split(",");
        for (String cr : grIds) {
          resourceService.insert(id, Optional.ofNullable(cr), Optional.empty(), Optional.empty(),
              Optional.empty());
        }
      }
    }

    // 更新班级详情表
    Optional<ClassDetail> classDetail = detailService.findByClassId(id);
    ClassDetail detail = null;
    if (classDetail.isPresent()) {
      detail = classDetail.get();
      coverId.ifPresent(detail::setCoverId);
      bannerId.ifPresent(detail::setBannerId);
      haveProvinceLeader.ifPresent(detail::setHaveProvinceLeader);
      haveMinister.ifPresent(detail::setHaveMinister);
      needGroupPhoto.ifPresent(detail::setNeedGroupPhoto);
      photoTime.ifPresent(detail::setPhotoTime);
      needVideo.ifPresent(detail::setNeedVideo);
      videoRequirement.ifPresent(detail::setVideoRequirement);
      needMakeCourse.ifPresent(detail::setNeedMakeCourse);
      courseVideoRequirement.ifPresent(detail::setCourseVideoRequirement);
      needNet.ifPresent(detail::setNeedNet);
      tableType.ifPresent(detail::setTableType);
      otherRequirement.ifPresent(detail::setOtherRequirement);
      if (!falg.isPresent()) {
        detail.setOtherRequirement(otherRequirement.orElse(null));
      }
      path.ifPresent(detail::setPath);
      coverPath.ifPresent(detail::setCoverPath);
      detailDao.update(detail);
    } else {
//	            detailService.insert(id, coverId, bannerId, Optional.empty(), haveProvinceLeader, haveMinister,
//	                    needGroupPhoto, photoTime, needVideo, videoRequirement, needMakeCourse, courseVideoRequirement,
//	                    needNet, tableType, otherRequirement, Optional.empty(), Optional.empty(), Optional.empty(),
//	                    Optional.empty(), path);
      detailService
          .insert(id, coverId, bannerId, Optional.empty(), haveProvinceLeader, haveMinister,
              needGroupPhoto, photoTime, needVideo, videoRequirement, needMakeCourse,
              courseVideoRequirement,
              needNet, tableType, otherRequirement, Optional.empty(), Optional.empty(),
              Optional.empty(),
              Optional.empty(), path, coverPath);
    }
    sender.send(MessageTypeContent.TRAIN_CLASS_UPDATE, MessageHeaderContent.ID, classInfo.getId());
    coverPath.ifPresent(c -> {
      sender.send(MessageTypeContent.TRAIN_UPDATE_CLASS_COVER,
          MessageHeaderContent.ID, coverId.get(),//封面图片的id
          MessageHeaderContent.PATH_PREFIX, c,//封面图片的静态路径
          MessageHeaderContent.CLASSID, id);
    });
    return classInfo;
  }

  @Override
  public void updateIsOverproof(String classId, Integer isOverproof) {
    Optional<ClassInfo> classInfo = classDao.fetchOne(CLASS_INFO.ID.eq(classId));
    classInfo.ifPresent(r -> {
      r.setIsOverproof(isOverproof);
      classDao.update(r);
    });
  }

  @Override
  public ClassInfo findByProjectContacts(String classId) {
    ClassInfo classInfo = classDao.execute(
        x -> x.select(Fields.start().add(PROJECT.CONTACT_MEMBER_ID).end()).from(CLASS_INFO)
            .leftJoin(PROJECT)
            .on(PROJECT.ID.eq(CLASS_INFO.PROJECT_ID)).where(CLASS_INFO.ID.eq(classId))
            .fetchOne(r -> {
              ClassInfo ci = new ClassInfo();
              ci.setContactPeople(r.getValue(PROJECT.CONTACT_MEMBER_ID));
              return ci;
            }));
    return classInfo;
  }

  @Override
  public PagedResult<ClassInfo> findQ(int page, int pageSize, Optional<String> name,
      Optional<String> MIScode,
      Optional<String> orgName, Optional<Integer> status, Optional<Long> reportBegin,
      Optional<Long> reportEnd,
      Optional<Long> returnBegin, Optional<Long> returnEnd, Optional<Integer> implementation_year,
      Optional<Integer> implementation_month, Optional<Integer> flag,
      List<String> organizationIds) {
    // TODO Auto-generated method stub
    List<String> memberIds = new ArrayList<>();
    List<String> orgIds = new ArrayList<>();
    com.zxy.product.train.jooq.tables.ClassEvaluate evaluate2 = CLASS_EVALUATE.as("evaluate2");
    Field<String> evaluate2Id = evaluate2.ID.as("evaluate2Id");
    List<ClassInfo> list = classDao.execute(d -> {
      Table<Record1<String>> basic = (d.select(CLASS_INFO.ID)
          .from(CLASS_INFO)
          .innerJoin(PROJECT).on(PROJECT.ID.eq(CLASS_INFO.PROJECT_ID))
          .leftJoin(CLASS_EVALUATE)
          .on(CLASS_INFO.ID.eq(CLASS_EVALUATE.CLASS_ID).and(CLASS_EVALUATE.TYPE.eq(6))
              .and(CLASS_EVALUATE.DELETE_FLAG.eq(0)))
          .leftJoin(evaluate2).on(CLASS_INFO.ID.eq(evaluate2.CLASS_ID).and(evaluate2.TYPE.eq(5))
              .and(evaluate2.DELETE_FLAG.eq(0)))
          .where(CLASS_INFO.DELETE_FLAG.eq(ClassInfo.DELETE_FLASE))
          .and(name.map(PROJECT.NAME::contains).orElse(DSL.trueCondition()))
          .and(MIScode.map(PROJECT.CODE::contains).orElse(DSL.trueCondition()))
          .and(implementation_month.map(CLASS_INFO.IMPLEMENTATION_MONTH::eq)
              .orElse(DSL.trueCondition()))
          .and(implementation_year.map(CLASS_INFO.IMPLEMENTATION_YEAR::eq)
              .orElse(DSL.trueCondition()))
          .and(reportBegin.map(CLASS_INFO.ARRIVE_DATE::ge).orElse(DSL.trueCondition()))
          .and(reportEnd.map(CLASS_INFO.ARRIVE_DATE::le).orElse(DSL.trueCondition()))
          .and(returnBegin.map(CLASS_INFO.RETURN_DATE::ge).orElse(DSL.trueCondition()))
          .and(returnEnd.map(CLASS_INFO.RETURN_DATE::le).orElse(DSL.trueCondition()))
          .and(PROJECT.ORGANIZATION_ID.in(organizationIds))
          .and(CLASS_INFO.DELETE_FLAG.eq(ClassInfo.DELETE_FLASE))
          .and(PROJECT.STATUS.eq(3))
          .and(status.map(CLASS_INFO.STATUS::eq).orElse(DSL.trueCondition()))
          .orderBy(CLASS_INFO.STATUS.sortAsc(2, 1, 3),
              DSL.when(CLASS_INFO.STATUS.eq(1), CLASS_INFO.IMPLEMENTATION_YEAR.neg())
                  .otherwise(CLASS_INFO.IMPLEMENTATION_YEAR).desc(),
              DSL.when(CLASS_INFO.STATUS.eq(1), CLASS_INFO.IMPLEMENTATION_MONTH.neg())
                  .otherwise(CLASS_INFO.IMPLEMENTATION_MONTH).desc())
          .limit((page - 1) * pageSize, pageSize)
      ).asTable("b");

      return d.select(Fields.start()
          .add(CLASS_INFO.ID, CLASS_INFO.PROJECT_ID, CLASS_INFO.STATUS, CLASS_INFO.ARRIVE_DATE,
              CLASS_INFO.ABILITY_SUBMIT_NUM, CLASS_INFO.FOUR_DEGREES_SUBMIT_NUM)
          .add(CLASS_INFO.IMPLEMENTATION_MONTH, CLASS_INFO.IMPLEMENTATION_YEAR,
              CLASS_INFO.RETURN_DATE, CLASS_INFO.QUESTIONNAIRE_STATUS)
          .add(PROJECT.CODE, PROJECT.NAME, PROJECT.CONTACT_PHONE, PROJECT.ORGANIZATION_ID,
              PROJECT.CONTACT_MEMBER_ID)
          .add(CLASS_EVALUATE.RELEASE)
          .add(evaluate2Id)
          .end())
          .from(CLASS_INFO)
          .innerJoin(basic).on(basic.field(CLASS_INFO.ID).eq(CLASS_INFO.ID))
          .innerJoin(PROJECT).on(PROJECT.ID.eq(CLASS_INFO.PROJECT_ID))
          .leftJoin(CLASS_EVALUATE)
          .on(CLASS_INFO.ID.eq(CLASS_EVALUATE.CLASS_ID).and(CLASS_EVALUATE.TYPE.eq(6))
              .and(CLASS_EVALUATE.DELETE_FLAG.eq(0)))
          .leftJoin(evaluate2).on(CLASS_INFO.ID.eq(evaluate2.CLASS_ID).and(evaluate2.TYPE.eq(5))
              .and(evaluate2.DELETE_FLAG.eq(0)))
          .fetch(r -> {
            ClassInfo cf = r.into(ClassInfo.class);
            cf.setClassName(r.getValue(PROJECT.NAME));
            cf.setCode(r.getValue(PROJECT.CODE));
            cf.setContactPhone(r.getValue(PROJECT.CONTACT_PHONE));// 需求方电话
            cf.setOrganizationId(r.getValue(PROJECT.ORGANIZATION_ID));
            cf.setContactPeople(r.getValue(PROJECT.CONTACT_MEMBER_ID));
            cf.setAbilitySubmitNum(r.getValue(CLASS_INFO.ABILITY_SUBMIT_NUM));
            cf.setStatus(r.getValue(CLASS_INFO.STATUS));//班级状态
            cf.setqStatus(r.getValue(CLASS_EVALUATE.RELEASE));//能力习得问卷发布状态
            cf.setQuestionnaireStatus(r.getValue(CLASS_INFO.QUESTIONNAIRE_STATUS));//是否有能力习得问卷
            Integer value = r.getValue(CLASS_INFO.FOUR_DEGREES_SUBMIT_NUM);
            if (value != null && value > 0) {
              cf.setfStatus(1);
            } else {
              cf.setfStatus(0);
            }
            String eId = r.getValue(evaluate2Id);
            if (eId != null) {
              cf.setfQuestionnaire(1);
            } else {
              cf.setfQuestionnaire(0);
            }
            memberIds.add(r.getValue(PROJECT.CONTACT_MEMBER_ID));
            orgIds.add(r.getValue(PROJECT.ORGANIZATION_ID));
            return cf;
          });
    });

    Map<String, Member> map = memberDao.execute(x -> x.select(MEMBER.ID, MEMBER.FULL_NAME)
        .from(MEMBER).where(MEMBER.ID.in(memberIds))).fetch(r -> r.into(Member.class)).stream()
        .collect(Collectors.toMap(Member::getId, o -> o));
    Map<String, Organization> orgMap = orgDao.execute(
        x -> x.select(ORGANIZATION.ID, ORGANIZATION.NAME).from(ORGANIZATION)
            .where(ORGANIZATION.ID.in(orgIds)))
        .fetch(r -> r.into(Organization.class)).stream()
        .collect(Collectors.toMap(Organization::getId, r -> r));

    Integer count = classDao.execute(d -> d.select(CLASS_INFO.ID.count())
        .from(CLASS_INFO)
        .innerJoin(PROJECT).on(PROJECT.ID.eq(CLASS_INFO.PROJECT_ID))
        .leftJoin(CLASS_EVALUATE)
        .on(CLASS_INFO.ID.eq(CLASS_EVALUATE.CLASS_ID).and(CLASS_EVALUATE.TYPE.eq(6))
            .and(CLASS_EVALUATE.DELETE_FLAG.eq(0)))
        .where(CLASS_INFO.DELETE_FLAG.eq(ClassInfo.DELETE_FLASE))
        .and(name.map(PROJECT.NAME::contains).orElse(DSL.trueCondition()))
        .and(MIScode.map(PROJECT.CODE::contains).orElse(DSL.trueCondition()))
        .and(implementation_month.map(CLASS_INFO.IMPLEMENTATION_MONTH::eq)
            .orElse(DSL.trueCondition()))
        .and(
            implementation_year.map(CLASS_INFO.IMPLEMENTATION_YEAR::eq).orElse(DSL.trueCondition()))
        .and(reportBegin.map(CLASS_INFO.ARRIVE_DATE::ge).orElse(DSL.trueCondition()))
        .and(reportEnd.map(CLASS_INFO.ARRIVE_DATE::le).orElse(DSL.trueCondition()))
        .and(returnBegin.map(CLASS_INFO.RETURN_DATE::ge).orElse(DSL.trueCondition()))
        .and(returnEnd.map(CLASS_INFO.RETURN_DATE::le).orElse(DSL.trueCondition()))
        .and(PROJECT.ORGANIZATION_ID.in(organizationIds))
        .and(CLASS_INFO.DELETE_FLAG.eq(ClassInfo.DELETE_FLASE))
        .and(PROJECT.STATUS.eq(3))
        .and(status.map(CLASS_INFO.STATUS::eq).orElse(DSL.trueCondition())))
        .fetchOne(CLASS_INFO.ID.count());

    return PagedResult.create(count, list.stream().map(m -> {
      if (map.get(m.getContactPeople()) != null) {
        m.setContactPeople(map.get(m.getContactPeople()).getFullName());
      }
      if (orgMap.get(m.getOrganizationId()) != null) {
        m.setOrganization(orgMap.get(m.getOrganizationId()).getName());
      }
      return m;
    }).collect(Collectors.toList()));
  }

  @Override
  public boolean getContactMemberId(String classId, String memberId) {
    List<ClassInfo> classInfoList = classDao.execute(
        x -> x.select(Fields.start().add(PROJECT.CONTACT_MEMBER_ID).end()).from(CLASS_INFO)
            .leftJoin(PROJECT)
            .on(PROJECT.ID.eq(CLASS_INFO.PROJECT_ID)).where(CLASS_INFO.ID.eq(classId))
            .and(PROJECT.CONTACT_MEMBER_ID.eq(memberId)).fetch(r -> {
              ClassInfo ci = new ClassInfo();
              ci.setContactPeople(r.getValue(PROJECT.CONTACT_MEMBER_ID));
              return ci;
            }));
    return classInfoList != null && classInfoList.size() > 0;
  }

  @Override
  public List<ClassInfo> findInfoToMis(Long startTime, Long endTime) {

    Field<String> orgName = ORGANIZATION.NAME.as("orgName");
    Field<String> memberName = MEMBER.NAME.as("memberName");
    Field<String> projectCode = PROJECT.CODE.as("projectCode");
    Field<String> orgCode = ORGANIZATION.CODE.as("orgCode");
    // String organizationPath = "1,10000002,0e1fc7d7-d8b5-48e1-8bfb-96ff5499ae31,4a4b4ae5-a8fb-4e7e-aaf0-cb7c99aeb219,";

    List<ClassInfo> classList = classDao.execute(x -> x
        .selectDistinct(Fields.start()
            .add(CLASS_INFO.ID, CLASS_INFO.ARRIVE_DATE, CLASS_INFO.RETURN_DATE,
                PROJECT.NAME, PROJECT.ORGANIZATION_ID, projectCode,
                PROJECT.DAYS, orgName, orgCode, memberName, MEMBER.FULL_NAME
            ).end())
        .from(CLASS_INFO)
        .leftJoin(PROJECT).on(PROJECT.ID.eq(CLASS_INFO.PROJECT_ID))
        .leftJoin(ORGANIZATION).on(ORGANIZATION.ID.eq(PROJECT.ORGANIZATION_ID))
        .leftJoin(MEMBER).on(PROJECT.CONTACT_MEMBER_ID.eq(MEMBER.ID))
        .where(CLASS_INFO.RETURN_DATE.gt(startTime))
        .and(CLASS_INFO.RETURN_DATE.le(endTime))
        .and(PROJECT.STATUS.eq(3))
        .orderBy(CLASS_INFO.CREATE_TIME.asc()))
        .fetch(r -> {
          ClassInfo ci = new ClassInfo();
          ci.setId(r.getValue(CLASS_INFO.ID));
          ci.setClassInfoType("内部培训");
          ci.setArriveDate(r.getValue(CLASS_INFO.ARRIVE_DATE));
          ci.setReturnDate(r.getValue(CLASS_INFO.RETURN_DATE));
          ci.setClassName(r.getValue(PROJECT.NAME));
          ci.setOrganizationId(r.getValue(orgCode));
          ci.setCode(r.getValue(projectCode));
          ci.setDays(r.getValue(PROJECT.DAYS));
          ci.setAddress("中国移动党校");
          ci.setOrganization(r.getValue(orgName));
          ci.setContactPeople(r.getValue(MEMBER.FULL_NAME));
          ci.setContactName(r.get(memberName));
          ci.setDuration(0d);

          List<Trainee> ee = new ArrayList<>();
          ci.setTraineeList(ee);
          return ci;
        });
    traineeDao.execute(r -> r
        .selectDistinct(Fields.start()
            .add(memberName, TRAINEE.CLASS_ID)
            .end())
        .from(TRAINEE)
        .leftJoin(MEMBER).on(TRAINEE.MEMBER_ID.eq(MEMBER.ID))
        .innerJoin(ORGANIZATION_DETAIL).on(MEMBER.ORGANIZATION_ID.eq(ORGANIZATION_DETAIL.SUB))
        .where(ORGANIZATION_DETAIL.ROOT.eq("129"))
        .and(TRAINEE.TYPE.eq(2)))
        .fetch(r -> {
          String classId = r.getValue(TRAINEE.CLASS_ID);
          for (ClassInfo classInfo : classList) {
            if (classInfo.getId().equals(classId)) {
              Trainee ee = new Trainee();
              ee.setMemberName(r.getValue(memberName));
              classInfo.getTraineeList().add(ee);
            }
          }
          return null;
        });

    classOfflineCourseDao.execute(r -> r
        .selectDistinct(Fields.start()
            .add(CLASS_OFFLINE_COURSE.START_TIME, CLASS_OFFLINE_COURSE.END_TIME,
                CLASS_OFFLINE_COURSE.CLASS_ID)
            .end())
        .from(CLASS_OFFLINE_COURSE))
        .fetch(r -> {
          String classId = r.getValue(CLASS_OFFLINE_COURSE.CLASS_ID);
          for (ClassInfo classInfo : classList) {
            if (classInfo.getId().equals(classId)) {
              ClassOfflineCourse off = new ClassOfflineCourse();
              try {

                off.setTime(getDuration(r.getValue(CLASS_OFFLINE_COURSE.START_TIME),
                    r.getValue(CLASS_OFFLINE_COURSE.END_TIME)));

              } catch (IllegalArgumentException | ParseException e) {
                // TODO Auto-generated catch block
                e.printStackTrace();
              }
              classInfo.setDuration(classInfo.getDuration() + off.getTime());
            }
          }
          return null;
        });
    return classList;
  }

  private Double getDuration(String start, String end) throws ParseException {

    SimpleDateFormat sdf = new SimpleDateFormat("HH:mm");

    Long startTime;
    long endTime;

    startTime = sdf.parse(start).getTime();
    endTime = sdf.parse(end).getTime();

    Double f = ((endTime - startTime) / (1000d)) / 3600d;

    BigDecimal bd = new BigDecimal(f);

    return bd.setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue();

  }

  @Override
  public ClassInfo updateCourseSalary(String id) {
    ClassInfo classInfo = classDao.get(id);
    classInfo.setCourseSalary(ClassInfo.COURSE_SALARY_ONE);
    classDao.update(classInfo);
    return classInfo;
  }

  @Override
  public void updateView(String id, Integer view) {
    Optional<ClassInfo> classInfo = classDao.getOptional(id);
    if(classInfo.isPresent()){
      classDao.execute(e->e.update(CLASS_INFO).set(CLASS_INFO.VIEW, view).where(CLASS_INFO.ID.eq(id)).execute());
    }
  }


  @Override
  public List<ClassInfo> filterByIds(String[] asList, String currentUserId) {

//			List<Record> step = classDao.execute(a -> a.selectDistinct(Fields.start()
//	                .add(CLASS_INFO.ID,CLASS_SIGNUP_INFO.IS_OPEN).end())
//	                .from(CLASS_INFO)
//	                .leftJoin(CLASS_SIGNUP_INFO)
//	                .on(CLASS_SIGNUP_INFO.CLASS_ID.eq(CLASS_INFO.ID))
//	                .where(CLASS_INFO.ID.in(asList))
//	                .and(CLASS_SIGNUP_INFO.IS_OPEN.eq(1))).fetch();

    List<Record> step2 = classDao.execute(b -> b.selectDistinct(Fields.start()
        .add(CLASS_INFO.ID, CLASS_SIGNUP_INFO.IS_OPEN).end())
        .from(CLASS_INFO)
        .leftJoin(CLASS_SIGNUP_INFO).on(CLASS_SIGNUP_INFO.CLASS_ID.eq(CLASS_INFO.ID))
        .leftJoin(TRAINEE).on(TRAINEE.CLASS_ID.eq(CLASS_INFO.ID))
        .leftJoin(CLASSSTAFF_CLASS).on(CLASSSTAFF_CLASS.CLASS_ID.eq(CLASS_INFO.ID))
        .where(CLASS_INFO.ID.in(asList))
//	                .and(CLASS_SIGNUP_INFO.IS_OPEN.eq(0))
        .and(TRAINEE.MEMBER_ID.eq(currentUserId)
            .and(TRAINEE.AUDIT_STATUS.eq(1))
            .and(TRAINEE.DELETE_FLAG.eq(0))
            .or(CLASSSTAFF_CLASS.MEMBER_ID.eq(currentUserId)
                .and(CLASSSTAFF_CLASS.DELETE_FLAG.eq(0))))).fetch();

//			step.addAll(step2);

    return step2.stream().map(r -> {
      ClassInfo classInfo = r.into(CLASS_INFO).into(ClassInfo.class);
      classInfo.setId(r.getValue(CLASS_INFO.ID, String.class));
      classInfo.setNotice(r.getValue(CLASS_SIGNUP_INFO.IS_OPEN, Integer.class));
      return classInfo;
    }).collect(Collectors.toList());
  }

  @Override
  public PagedResult<ClassInfo> frontResponse(int page, int pageSize, Optional<String> MIScode,
      Optional<String> className, Optional<Integer> classStatus) {
    // TODO Auto-generated method stub
    // 重命名
    Field<String> orgName = ORGANIZATION.NAME.as("orgName");
    Field<String> projectId = PROJECT.ID.as("projectId");
    Field<String> questionnaireId = RESEARCH_QUESTIONARY.ID.as("questionnaireId");
    Field<Integer> questionnaireType = RESEARCH_QUESTIONARY.TYPE.as("questionnaireType");
    // 构建查询语句
    SelectOnConditionStep<Record> step = classDao.execute(x -> x
        .select(Fields.start()
            .add(CLASS_INFO.ID, projectId, PROJECT.CODE, PROJECT.NAME, PROJECT.AMOUNT, orgName,
                MEMBER.FULL_NAME, PROJECT.CONTACT_PHONE, CLASS_INFO.ARRIVE_DATE,
                CLASS_INFO.RETURN_DATE,
                CLASS_INFO.STATUS, CLASS_INFO.TRAINEE_NUM, CLASS_INFO.NOTICE, questionnaireId,
                questionnaireType,PROJECT.IS_PARTY_CADRE)
            .end())
        .from(CLASS_INFO)
        .leftJoin(PROJECT).on(CLASS_INFO.PROJECT_ID.eq(PROJECT.ID))
        .leftJoin(ORGANIZATION).on(PROJECT.ORGANIZATION_ID.eq(ORGANIZATION.ID))
        .leftJoin(MEMBER).on(PROJECT.CONTACT_MEMBER_ID.eq(MEMBER.ID))
        .leftJoin(RESEARCH_QUESTIONARY).on(RESEARCH_QUESTIONARY.CLASS_ID.eq(CLASS_INFO.ID)
                    .and(RESEARCH_QUESTIONARY.IS_ENSEMBLE.eq(1))
            .and(RESEARCH_QUESTIONARY.TYPE.eq(DSL
                .when(CLASS_INFO.ARRIVE_DATE.lt(ClassInfo.SATISFACTION_TIME),
                    ResearchQuestionary.TYPE_SATISFACTION_QUESTIONARY)
                .otherwise(ResearchQuestionary.TYPE_NEW_SATISFACTION_QUESTIONARY)))
        ).leftJoin(CLASS_EVALUATE).on(CLASS_EVALUATE.RESOURCE_ID.eq(RESEARCH_QUESTIONARY.ID)
                    .and(CLASS_EVALUATE.TYPE.eq(DSL
                            .when(CLASS_INFO.ARRIVE_DATE.lt(ClassInfo.SATISFACTION_TIME),
                                    ResearchQuestionary.TYPE_SATISFACTION_QUESTIONARY)
                            .otherwise(ResearchQuestionary.TYPE_NEW_SATISFACTION_QUESTIONARY)))
                    .and(CLASS_EVALUATE.DELETE_FLAG.eq(0))
                    .and(CLASS_EVALUATE.CLASS_ID.eq(CLASS_INFO.ID)))
    );
    // 组合条件
    Stream<Optional<Condition>> conditions = Stream.of(
        MIScode.map(PROJECT.CODE::contains),
        className.map(PROJECT.NAME::contains),
        Optional.of(DELETE_FLASE).map(CLASS_INFO.DELETE_FLAG::eq));
    // 合并条件
    Condition c = conditions.filter(Optional::isPresent).map(Optional::get)
        .reduce((acc, item) -> acc.and(item))
        .orElse(DSL.trueCondition());
    SelectConditionStep<Record> condStep = step.where(c);
    if (classStatus.isPresent()) {
      condStep.and(CLASS_INFO.STATUS.eq(classStatus.get()));
    }
    // 获取行数
    Integer count = classDao.execute(e -> e.fetchCount(condStep));
    //排序
    if (classStatus.isPresent()) {
      if (classStatus.get() == STATUS_FALSE) {//未实施
        condStep.orderBy(CLASS_INFO.SORT.asc(), CLASS_INFO.ARRIVE_DATE.asc());
      }
      if (classStatus.get() == STATUS_ING) {//实施中
        condStep.orderBy(CLASS_INFO.SORT.asc(), CLASS_INFO.ARRIVE_DATE.asc());
      }
      if (classStatus.get() == STATUS_TRUE) {//已实施
        condStep.orderBy(CLASS_INFO.SORT.asc(), CLASS_INFO.ARRIVE_DATE.asc());
      }
    } else {
      condStep.orderBy(CLASS_INFO.SORT.asc(), CLASS_INFO.ARRIVE_DATE.desc());
    }
    // 获取列表
    List<ClassInfo> list = condStep.limit((page - 1) * pageSize, pageSize).fetch(r -> {
      ClassInfo ci = r.into(CLASS_INFO).into(ClassInfo.class);
      ci.setId(r.getValue(CLASS_INFO.ID));
      ci.setClassName(r.getValue(PROJECT.NAME));
      long value = r.getValue(CLASS_INFO.ARRIVE_DATE) + 86400000;
      long currentTimeMillis = System.currentTimeMillis();
      if (value >= currentTimeMillis) {
        ci.setStatus(1);
      } else {
        ci.setStatus(r.getValue(CLASS_INFO.STATUS));
      }
      ci.setCode(r.getValue(PROJECT.CODE));
      ci.setAmount(r.getValue(PROJECT.AMOUNT));
      ci.setOrganization(r.getValue(orgName));
      ci.setContactPeople(r.getValue(MEMBER.FULL_NAME));
      ci.setContactPhone(r.getValue(PROJECT.CONTACT_PHONE));
      ci.setArriveDate(r.getValue(CLASS_INFO.ARRIVE_DATE));
      // ci.setReturnDate(r.getValue(CLASS_INFO.RETURN_DATE));
//            ci.setRegistNumber(r.getValue(TRAINEE.ID.countDistinct()));
      ci.setProjectId(r.getValue(projectId));
      ci.setQuestionaryId(r.getValue(questionnaireId));
      ci.setQuestionaryType(r.getValue(questionnaireType));
      ci.setIsPartyCadre(r.getValue(PROJECT.IS_PARTY_CADRE));//添加是否是党校培训班字段
      return ci;
    });
//        if (list != null && list.size() > 0) {
//        	logger.error("count:{},集合长度:{},时间:{}",count,list.size(),new Date());
//        	if (classStatus.isPresent()) {
//        		for (int i = 0; i < list.size(); i++) {
//           		 logger.error("查询状态:{},班级名称:{},时间:{}",classStatus.get(),list.get(i).getClassName(),new Date());
//        		}
//        	} else {
//        		logger.error("状态为全部的count:{},集合长度:{},时间:{}",count,list.size(),new Date());
//        	}
//        }
    return PagedResult.create(count, list);
  }

  @Override
  public Map<String, String> findByClassId(String id) {
    ClassInfo classInfo = classDao.execute(x -> x
        .select(Fields.start()
            .add(CLASS_INFO.ID, PROJECT.CODE, PROJECT.NAME)
            .end())
        .from(CLASS_INFO)
        .leftJoin(PROJECT).on(CLASS_INFO.PROJECT_ID.eq(PROJECT.ID)).where(CLASS_INFO.ID.eq(id))
        .fetchOne(r -> {
          ClassInfo ci = r.into(ClassInfo.class);
          ci.setCode(r.getValue(PROJECT.CODE));
          return ci;
        }));
    Map map = new HashMap();
    map.put(classInfo.getCode(), classInfo.getCode());
    return map;
  }

  @Override
  public ClassInfo findByTime(String id) {
    return classDao.execute(x -> x
        .select(Fields.start()
            .add(CLASS_INFO.ID, CLASS_INFO.ARRIVE_DATE, CLASS_INFO.RETURN_DATE)
            .end())
        .from(CLASS_INFO).where(CLASS_INFO.ID.eq(id)).fetchOne(r -> {
          ClassInfo ci = new ClassInfo();
          ci.setId(r.getValue(CLASS_INFO.ID));
          ci.setArriveDate(r.getValue(CLASS_INFO.ARRIVE_DATE));
          ci.setReturnDate(r.getValue(CLASS_INFO.RETURN_DATE));
          return ci;
        }));
  }

  @Override
  public Map<String,String> findByProjectIds(List<String> ids) {
      Map map =  new HashMap();
      classDao.execute(x -> x
              .select(Fields.start()
                      .add(PROJECT.ID,CLASS_INFO.ID)
                      .end())
              .from(PROJECT).leftJoin(CLASS_INFO).on(CLASS_INFO.PROJECT_ID.eq(PROJECT.ID)).where(PROJECT.ID.in(ids)).and(PROJECT.STATUS.eq(ClassInfo.STATUS_TRUE)).fetch(r->{
                  map.put(r.getValue(PROJECT.ID),r.getValue(CLASS_INFO.ID));
                  return null;
              }));
      return map;
  }

  @Override
  public List<String> getAllClassIds(int start, int limit) {
    return classDao.execute(dslContext -> {
      return dslContext.select(CLASS_INFO.ID)
              .from(CLASS_INFO)
              .where(CLASS_INFO.DELETE_FLAG.eq(ClassInfo.DELETE_FLASE)).limit(start, limit).fetch(CLASS_INFO.ID);
    });
  }

  @Override
  public Optional<ClassInfo> getDetails(String classId) {
    return classDao.getOptional(classId);
  }

  //判断党校进修班 学员完成问卷状态
  private boolean existMemberSubmitStatus(String classId,List<String> memberIds)throws UnprocessableException{
    List<String> statusList=new ArrayList<>();
    for(String memberId:memberIds){
      List<ResearchRecord> recordList=questionnaireQuestionTypeService.getResearchRecordByClassId(classId,memberId);
      if(recordList!=null&&recordList.isEmpty()){
        List<ResearchRecord> noSubmit=recordList.stream().filter(r ->r.getStatus()==null||r.getStatus()==0)
                .collect(Collectors.toList());
        if(noSubmit==null||noSubmit.isEmpty()){
          statusList.add("1");
        }else{
          statusList.add("0");
        }
      }
    }
    if(statusList.isEmpty()||statusList.contains("0")){
      return false;
    }else{
      return true;
    }
  }

  @Override
  public List<ClassInfo> classExperience(String memberId) {
    List<ClassInfo> classInfoList = classDao.execute(r -> r.select(CLASS_INFO.ID.as("classId"),
                    PROJECT.ID,
                    PROJECT.NAME.as("projectName"),
                    PROJECT.YEAR,
                    PROJECT.MONTH,
                    PROJECT.DAYS,
                    ORGANIZATION.NAME,
                    CLASS_INFO.ARRIVE_DATE,
                    CLASS_INFO.RETURN_DATE)
            .from(TRAINEE)
            .leftJoin(CLASS_INFO).on(TRAINEE.CLASS_ID.eq(CLASS_INFO.ID))
            .leftJoin(PROJECT).on(CLASS_INFO.PROJECT_ID.eq(PROJECT.ID))
            .leftJoin(ORGANIZATION).on(ORGANIZATION.ID.eq(PROJECT.ORGANIZATION_ID))
            .where(TRAINEE.MEMBER_ID.eq(memberId))
            .and(CLASS_INFO.STATUS.eq(STATUS_TRUE))
            .and(CLASS_INFO.DELETE_FLAG.ne(DELETE_TRUE))
            .and(TRAINEE.TYPE.eq(TRAINEE_TYPE_FORMAL))
            .and(TRAINEE.AUDIT_STATUS.eq(AUDIT_AGREE))
            .orderBy(CLASS_INFO.ARRIVE_DATE.desc())
            .fetch(o -> {
              ClassInfo classInfo = new ClassInfo();
              classInfo.setId(o.getValue(CLASS_INFO.ID.as("classId")));
              classInfo.setOrganization(o.getValue(ORGANIZATION.NAME));
              classInfo.setArriveDate(o.getValue(CLASS_INFO.ARRIVE_DATE));
              classInfo.setReturnDate(o.getValue(CLASS_INFO.RETURN_DATE));
              Project project = new Project();
              project.setId(o.getValue(PROJECT.ID));
              project.setName(o.getValue(PROJECT.NAME.as("projectName")));
              project.setYear(o.getValue(PROJECT.YEAR));
              project.setMonth(o.getValue(PROJECT.MONTH));
              project.setDays(o.getValue(PROJECT.DAYS));
              classInfo.setProject(project);
              return classInfo;
            }));
    List<String> classIds = classInfoList.stream().map(ClassInfoEntity::getId).collect(Collectors.toList());



    Map<String, List<ClassOfflineCourse>> classIdToCourseDatesMap = classOfflineCourseCommonDao.execute(r ->
                    r.select(CLASS_OFFLINE_COURSE.CLASS_ID,
                                    CLASS_OFFLINE_COURSE.START_TIME,
                                    CLASS_OFFLINE_COURSE.END_TIME,
                                    CLASS_OFFLINE_COURSE.COURSE_DATE)
                            .from(CLASS_OFFLINE_COURSE)
                            .where(CLASS_OFFLINE_COURSE.CLASS_ID.in(classIds))
                            .and(CLASS_OFFLINE_COURSE.DELETE_FLAG.eq(ClassOfflineCourse.DELETE_FALSE))
                            .fetch())
            .stream()
            .collect(Collectors.groupingBy(
                    o -> o.getValue(CLASS_OFFLINE_COURSE.CLASS_ID),
                    Collectors.mapping(
                            o -> {
                              ClassOfflineCourse classOfflineCourse = new ClassOfflineCourse();
                              classOfflineCourse.setId(o.getValue(CLASS_OFFLINE_COURSE.CLASS_ID));
                              classOfflineCourse.setStartTime(o.getValue(CLASS_OFFLINE_COURSE.START_TIME));
                              classOfflineCourse.setEndTime(o.getValue(CLASS_OFFLINE_COURSE.END_TIME));
                              classOfflineCourse.setCourseDate(o.getValue(CLASS_OFFLINE_COURSE.COURSE_DATE));
                              return classOfflineCourse;
                            },
                            Collectors.toList()
                    )
            ));
    Map<String, Double> classIdToDistinctDaysCountMap = convertMillisecondsToDateAndCount(classIdToCourseDatesMap);
    for (ClassInfo classInfo : classInfoList) {
      Project project = classInfo.getProject();
      Double aDouble = classIdToDistinctDaysCountMap.get(classInfo.getId());
      if(ObjectUtils.isEmpty(aDouble)){
        project.setDays(0);
      }else {
        project.setDays((int)(aDouble * 100));
      }
      classInfo.setProject(project);
    }
    return classInfoList;
  }


  private Map<String, Double> convertMillisecondsToDateAndCount(Map<String, List<ClassOfflineCourse>> classIdToCourseDatesMap){
    return classIdToCourseDatesMap.entrySet().stream()
            .collect(Collectors.toMap(
                    Map.Entry::getKey, // key: classId
                    entry -> entry.getValue().stream()
                            .collect(Collectors.groupingBy(
                                    classOfflineCourse -> classOfflineCourse.getCourseDate() / (24 * 60 * 60 * 1000), // 使用毫秒值去掉时分秒
                                    Collectors.toList()
                            ))
                            .values().stream()
                            .mapToDouble(courseList -> {
                              // 判断一天内是否有上午和下午的课程
                              boolean hasMorning = false;
                              boolean hasAfternoon = false;

                              for (ClassOfflineCourse classOfflineCourse : courseList) {
                                LocalTime startTime = LocalTime.parse(classOfflineCourse.getStartTime());
                                LocalTime endTime = LocalTime.parse(classOfflineCourse.getEndTime());

                                if (startTime.isBefore(LocalTime.NOON) && endTime.isAfter(LocalTime.NOON)) {
                                  hasMorning = true;
                                  hasAfternoon = true;
                                } else if (startTime.isBefore(LocalTime.NOON)) {
                                  hasMorning = true;
                                } else if (startTime.isAfter(LocalTime.NOON)) {
                                  hasAfternoon = true;
                                }
                              }

                              // 判断是否全天或半天
                              if (hasMorning && hasAfternoon) {
                                return 1.0; // 全天
                              } else {
                                return 0.5; // 半天
                              }
                            })
                            .sum() // 计算总天数
            ));
  }

    @Override
  public ResearchQuestionary getQuestionnaireScores(String memberId, String classId) {
    return classDao.execute(r ->
            r.select(RESEARCH_ANSWER_RECORD.ANSWER.sum(), RESEARCH_RECORD.ID)
                    .from(RESEARCH_QUESTIONARY)
                    .leftJoin(RESEARCH_RECORD).on(RESEARCH_QUESTIONARY.ID.eq(RESEARCH_RECORD.RESEARCH_QUESTIONARY_ID))
                    .leftJoin(RESEARCH_ANSWER_RECORD).on(RESEARCH_RECORD.ID.eq(RESEARCH_ANSWER_RECORD.RESEARCH_RECORD_ID))
                    .leftJoin(QUESTIONNAIRE_QUESTION_TYPE).on(RESEARCH_ANSWER_RECORD.QUESTIONNAIRE_QUESTION_ID.eq(QUESTIONNAIRE_QUESTION_TYPE.ID))
                    .where(RESEARCH_QUESTIONARY.CLASS_ID.eq(classId))
                    .and(RESEARCH_RECORD.MEMBER_ID.eq(memberId))
                    .and(QUESTIONNAIRE_QUESTION_TYPE.LEVEL.eq(Short.parseShort(QuestionnaireQuestion.QUESTION_LEVEL_COURSE.toString())))
                    .fetchOne(o->{
                      ResearchQuestionary researchQuestionary = new ResearchQuestionary();
                      researchQuestionary.setrId(o.getValue(RESEARCH_RECORD.ID));
                      BigDecimal decima = o.getValue(RESEARCH_ANSWER_RECORD.ANSWER.sum());
                      researchQuestionary.setScore(decima != null ? decima.setScale(0, RoundingMode.HALF_UP) : BigDecimal.valueOf(0));
                      return researchQuestionary;
                    }));
  }

  @Override
  public ClassInfo getClassAndMemberDetail(String memberId, String classId) {

      return classDao.execute(r ->
            r.select(CLASS_INFO.ID,
                            CLASS_INFO.ARRIVE_DATE,
                            CLASS_INFO.RETURN_DATE,
                            ORGANIZATION.NAME,
                            CONFIGURATION_VALUE.NAME.as("address"))
                    .from(TRAINEE)
                    .leftJoin(CLASS_INFO).on(TRAINEE.CLASS_ID.eq(CLASS_INFO.ID))
                    .leftJoin(PROJECT).on(CLASS_INFO.PROJECT_ID.eq(PROJECT.ID))
                    .leftJoin(ORGANIZATION).on(ORGANIZATION.ID.eq(PROJECT.ORGANIZATION_ID))
                    .leftJoin(CONFIGURATION_VALUE).on(CONFIGURATION_VALUE.ID.eq(PROJECT.ADDRESS))
                    .where(TRAINEE.MEMBER_ID.eq(memberId))
                    .and(CLASS_INFO.ID.eq(classId))
                    .fetchOne(o -> {
                      ClassInfo classInfo = new ClassInfo();
                      classInfo.setId(o.getValue(CLASS_INFO.ID));
                      classInfo.setArriveDate(o.getValue(CLASS_INFO.ARRIVE_DATE));
                      classInfo.setReturnDate(o.getValue(CLASS_INFO.RETURN_DATE));
                      classInfo.setOrganization(o.getValue(ORGANIZATION.NAME));
                      classInfo.setAddress(o.getValue(CONFIGURATION_VALUE.NAME.as("address")));
                      return classInfo;
                    }));
  }


  /**
   * 查询需要处理的用户与培训班信息
   *
   * @param startTime
   * @param endTime
   * @return
   */
  @Override
  public Boolean dealClassInfo(Long startTime, Long endTime) {

    //key memeberId value classIds
    final Map<String, List<String>> memberClassInfoIdMap = new HashMap<>();

    //key  classId
    Set<String> ClassInfoIdSet = new HashSet<>();
    Set<String> memberIdSet = new HashSet<>();
    //先查询需要处理的用户id 和 培训班id
    classDao.execute(r -> r.select(
          CLASS_INFO.ID.as("classId"), TRAINEE.MEMBER_ID,CLASS_INFO.ARRIVE_DATE)
          .from(TRAINEE)
          .leftJoin(CLASS_INFO).on(TRAINEE.CLASS_ID.eq(CLASS_INFO.ID))
          .leftJoin(PROJECT).on(CLASS_INFO.PROJECT_ID.eq(PROJECT.ID))
          .where(CLASS_INFO.STATUS.eq(STATUS_TRUE))
          .and(CLASS_INFO.DELETE_FLAG.ne(DELETE_TRUE))
          .and(TRAINEE.TYPE.eq(TRAINEE_TYPE_FORMAL))
          .and(TRAINEE.AUDIT_STATUS.eq(AUDIT_AGREE))
          .and(CLASS_INFO.ARRIVE_DATE.between(startTime, endTime).or(CLASS_INFO.RETURN_DATE.between(startTime, endTime)))
          .orderBy(CLASS_INFO.ARRIVE_DATE.desc())
          .fetch(o -> {

              String memberId = o.getValue(TRAINEE.MEMBER_ID);
              String classId = o.getValue(CLASS_INFO.ID.as("classId"));

              ClassInfoIdSet.add(classId);
              memberIdSet.add(memberId);
              //memberId不存在,则创建
              if (memberClassInfoIdMap.get(memberId) == null){

                  List<String> classInfoIds = new ArrayList<>();
                  classInfoIds.add(classId);

                  memberClassInfoIdMap.put(memberId, classInfoIds);
              }else {
                List<String> classInfoIds = memberClassInfoIdMap.get(memberId);
                classInfoIds.add(classId);
              }
              return memberId;
          })
    );

    //获取培训班培训时长
    Map<String, List<ClassOfflineCourse>> classIdToCourseDatesMap = classOfflineCourseCommonDao.execute(r ->
            r.select(CLASS_OFFLINE_COURSE.CLASS_ID,
                    CLASS_OFFLINE_COURSE.START_TIME,
                    CLASS_OFFLINE_COURSE.END_TIME,
                    CLASS_OFFLINE_COURSE.COURSE_DATE)
                    .from(CLASS_OFFLINE_COURSE)
                    .where(CLASS_OFFLINE_COURSE.CLASS_ID.in(ClassInfoIdSet))
                    .and(CLASS_OFFLINE_COURSE.DELETE_FLAG.eq(ClassOfflineCourse.DELETE_FALSE))
                    .fetch())
            .stream()
            .collect(Collectors.groupingBy(
                    o -> o.getValue(CLASS_OFFLINE_COURSE.CLASS_ID),
                    Collectors.mapping(
                            o -> {
                              ClassOfflineCourse classOfflineCourse = new ClassOfflineCourse();
                              classOfflineCourse.setId(o.getValue(CLASS_OFFLINE_COURSE.CLASS_ID));
                              classOfflineCourse.setStartTime(o.getValue(CLASS_OFFLINE_COURSE.START_TIME));
                              classOfflineCourse.setEndTime(o.getValue(CLASS_OFFLINE_COURSE.END_TIME));
                              classOfflineCourse.setCourseDate(o.getValue(CLASS_OFFLINE_COURSE.COURSE_DATE));
                              return classOfflineCourse;
                            },
                            Collectors.toList()
                    )
            ));

    //key 培训班id value 培训时长
    Map<String, Double> classIdToDistinctDaysCountMap = this.convertMillisecondsToDateAndCountByDate(classIdToCourseDatesMap,endTime);

    //获取上个月的年月信息
    Date date = new Date(startTime);
    //获取上个月的年月 startTime
    String yearMonth = DateUtil.format(date, DateUtil.DATE_PATTERN_YYYYMM);
    String year = DateUtil.format(date, DateUtil.DATE_PATTERN_YYYY);
    String month = DateUtil.format(date, DateUtil.DATE_PATTERN_MM);


    List<StudyReportTrain> insertList = new ArrayList<>();
    //查询培训班信息和培训班培训地点,培训班名称
    Map<String, ClassInfo> classInfoMap = this.mapClassInfoByClassIdSet(ClassInfoIdSet);

    //每个用户的课程数查询
    Map<String, Integer> memberCourseCountMap = this.mapCourseCountByMemberId(memberIdSet, ClassInfoIdSet, startTime, endTime);

    //查询用户的签到率
    Map<String, String> memberSignMap = this.mapSignRateByMemberIds(memberIdSet, ClassInfoIdSet, startTime, endTime);

    //满意度查询
    Map<String, BigDecimal> satisfactionMap = this.getSatisfactionSum(memberIdSet, ClassInfoIdSet);

    //循环封装用户数据
    for (String memberId : memberClassInfoIdMap.keySet()) {

      Integer trainDayTotal = 0;

      int i = 0;
      List<String> classInfoIds = memberClassInfoIdMap.get(memberId);


      StudyReportTrain insertEntiy = new StudyReportTrain();

      insertEntiy.forInsert();
      insertEntiy.setMemberId(memberId);
      insertEntiy.setYearMonth(yearMonth);
      String className = null;
      List<ClassInfoDTO> classInfoDtoList = new ArrayList<>();


      for (String classInfoId : classInfoIds) {

        ClassInfo classInfo = classInfoMap.get(classInfoId);

        //培训班名称拼接
        if (classInfoMap.get(classInfoId) != null && i < 3){

          className = className == null ? classInfo.getProjectName() : className + "、" + classInfo.getProjectName();
          ++i;
        }

        ClassInfoDTO classInfoDTO = new ClassInfoDTO();
        //培训班详情拼接
        classInfoDTO.setArriveDate(classInfo.getArriveDate());
        classInfoDTO.setReturnDate(classInfo.getReturnDate());
        classInfoDTO.setTrainingTypeName(classInfo.getTrainingTypeName());
        classInfoDTO.setProjectName(classInfo.getProjectName());
        //参训时长
        Double aDouble = classIdToDistinctDaysCountMap.get(classInfoId);
        if(ObjectUtils.isEmpty(aDouble)){
          classInfoDTO.setTrainDay("0");
        }else {
          classInfoDTO.setTrainDay(Integer.toString((int)(aDouble * 100)));
          trainDayTotal += (int)(aDouble * 100);
        }

        classInfoDtoList.add(classInfoDTO);
      }

      //培训班名称拼接的字符串
      insertEntiy.setClassName(className);
      //培训班数量
      insertEntiy.setClassNumber(classInfoIds.size());
      //培训班详情
      insertEntiy.setClassDetail(JSONObject.toJSONString(classInfoDtoList));
      //参训总时长
      insertEntiy.setTrainDay(Integer.toString(trainDayTotal));
      //累计学习课程数
      insertEntiy.setCourseNumber(memberCourseCountMap.get(memberId));

      //平均考勤率
      insertEntiy.setAvgSign(memberSignMap.get(memberId) == null ? "0" : memberSignMap.get(memberId));
      //平均满意度
      insertEntiy.setAvgSatisfaction(satisfactionMap.get(memberId) != null? satisfactionMap.get(memberId).toString() : "0");

      insertList.add(insertEntiy);
    }


    int batchSize = 5000;
    int totalSize = insertList.size();
    int batches = (int) Math.ceil((double) totalSize / batchSize);

    for (int i = 0; i < batches; i++) {
      int start = i * batchSize;
      int end = Math.min((i + 1) * batchSize, totalSize);
      List<StudyReportTrain> batchList = insertList.subList(start, end);

      // 转换为可更新记录并批量插入
      List<UpdatableRecord<?>> updatableRecords = batchList.stream()
              .map(studyReportTrain -> studyReportTrain.translate2UpdateRecord(year))
              .collect(Collectors.toList());
      // 执行批量插入
      studyReportTrainDao.execute(dsl -> dsl.batchInsert(updatableRecords).execute());
    }

    return true;
  }


  /**
   * 每个用户的课程数查询
   * @param memberIds
   * @param classInfoIdSet
   * @param startTime
   * @param endTime
   * @return
   */
  private Map<String, Integer> mapCourseCountByMemberId(Set<String> memberIds, Set<String> classInfoIdSet, Long startTime, Long endTime) {
    Field<Integer> courseCount = Tables.CLASS_OFFLINE_COURSE.ID.count().as("courseCount");

    Result<Record2<String, Integer>> result = classOfflineCourseCommonDao.execute(e -> e.select(Tables.TRAINEE.MEMBER_ID, courseCount))
            .from(Tables.TRAINEE).leftJoin(CLASS_INFO).on(Tables.TRAINEE.CLASS_ID.eq(CLASS_INFO.ID))
            .leftJoin(Tables.CLASS_OFFLINE_COURSE).on(Tables.CLASS_OFFLINE_COURSE.CLASS_ID.eq(CLASS_INFO.ID))
            .where(Tables.TRAINEE.MEMBER_ID.in(memberIds).and(CLASS_INFO.STATUS.eq(STATUS_TRUE)).and(CLASS_INFO.ID.in(classInfoIdSet))
                    .and(Tables.TRAINEE.TYPE.eq(TRAINEE_TYPE_FORMAL))
                    .and(Tables.CLASS_OFFLINE_COURSE.DELETE_FLAG.eq(DELETE_FLASE))
                    .and(Tables.CLASS_OFFLINE_COURSE.COURSE_DATE.between(startTime, endTime))
            )
            .groupBy(Tables.TRAINEE.MEMBER_ID)
            .fetch();
    Map<String, Integer> resultMap = result.intoMap(Tables.TRAINEE.MEMBER_ID, courseCount);
    return resultMap;
  }


  /**
   * 查询培训班信息和培训班培训地点,培训班名称
   * @param classInfoIdSet
   */
  private Map<String, ClassInfo> mapClassInfoByClassIdSet(Set<String> classInfoIdSet) {

      List<ClassInfo> classInfoList = classDao.execute(r -> r.select(
              CLASS_INFO.ID.as("classId"), CLASS_INFO.ARRIVE_DATE,
              CLASS_INFO.RETURN_DATE, PROJECT.NAME.as("projectName"),
              CONFIGURATION_VALUE.NAME.as("trainingTypeName"))
              .from(CLASS_INFO)
              .leftJoin(PROJECT).on(CLASS_INFO.PROJECT_ID.eq(PROJECT.ID))
              .leftJoin(CONFIGURATION_VALUE).on(PROJECT.TYPE_ID.eq(CONFIGURATION_VALUE.ID))
              .where(CLASS_INFO.ID.in(classInfoIdSet))
              .groupBy(CLASS_INFO.ID.as("classId"))
              .orderBy(CLASS_INFO.ARRIVE_DATE.desc())
              .fetch(o -> {
                ClassInfo classInfo = new ClassInfo();
                classInfo.setId(o.getValue(CLASS_INFO.ID.as("classId")));
                classInfo.setArriveDate(o.getValue(CLASS_INFO.ARRIVE_DATE));
                classInfo.setReturnDate(o.getValue(CLASS_INFO.RETURN_DATE));
                classInfo.setProjectName("《"+ o.getValue(PROJECT.NAME.as("projectName"))+ "》");//培训名称
                classInfo.setTrainingTypeName(o.getValue(CONFIGURATION_VALUE.NAME.as("trainingTypeName")));//培训地点
                return classInfo;
              })
        );


      Map<String, ClassInfo> resultMap = new HashMap<>(classInfoList.size());

      for (ClassInfo classInfo : classInfoList) {

          resultMap.put(classInfo.getId(), classInfo);
      }

      return resultMap;
  }
  /**
   * 带时间的班级培训时长过滤
   * @param classIdToCourseDatesMap
   * @param endDate
   * @return
   */
  private Map<String, Double> convertMillisecondsToDateAndCountByDate(Map<String, List<ClassOfflineCourse>> classIdToCourseDatesMap, Long endDate){
      return classIdToCourseDatesMap.entrySet().stream()
            .collect(Collectors.toMap(
                    Map.Entry::getKey, // key: classId
                    entry -> entry.getValue().stream()
                            .filter(classOfflineCourse -> classOfflineCourse.getCourseDate() <= endDate)
                            .collect(Collectors.groupingBy(
                                    classOfflineCourse -> classOfflineCourse.getCourseDate() / (24 * 60 * 60 * 1000), // 使用毫秒值去掉时分秒
                                    Collectors.toList()
                            ))
                            .values().stream()
                            .mapToDouble(courseList -> {
                              // 判断一天内是否有上午和下午的课程
                              boolean hasMorning = false;
                              boolean hasAfternoon = false;

                              for (ClassOfflineCourse classOfflineCourse : courseList) {
                                LocalTime startTime = LocalTime.parse(classOfflineCourse.getStartTime());
                                LocalTime endTime = LocalTime.parse(classOfflineCourse.getEndTime());

                                if (startTime.isBefore(LocalTime.NOON) && endTime.isAfter(LocalTime.NOON)) {
                                  hasMorning = true;
                                  hasAfternoon = true;
                                } else if (startTime.isBefore(LocalTime.NOON)) {
                                  hasMorning = true;
                                } else if (startTime.isAfter(LocalTime.NOON)) {
                                  hasAfternoon = true;
                                }
                              }

                              // 判断是否全天或半天
                              if (hasMorning && hasAfternoon) {
                                return 1.0; // 全天
                              } else {
                                return 0.5; // 半天
                              }
                            })
                            .sum() // 计算总天数
            ));
  }

  /**
   * 考勤率查询
   * @param memberIdSet
   * @param classInfoIdSet
   * @return
   */
  private Map<String, String> mapSignRateByMemberIds(Set<String> memberIdSet, Set<String> classInfoIdSet, Long startTime, Long endTime) {

    Map<String, String> resultMap = new HashMap<>();

    Map<String, Integer> signedCountMap = new HashMap<>();
    Map<String, Integer> signCountMap = new HashMap<>();

    //班级已经做了处理,由于业务原因人员无法使用in统计,否则会造成统计错误
    for (String memberId : memberIdSet) {

      //已实施培训班所有考勤为已签到的次数
      Map<String, Integer> signedCountMapReslt = traineeCommonDao.execute(e -> e.select(Tables.TRAINEE.MEMBER_ID, SIGN_DETAIL.ID.count()))
              .from(Tables.TRAINEE).leftJoin(CLASS_INFO).on(Tables.TRAINEE.CLASS_ID.eq(CLASS_INFO.ID))
              .leftJoin(Tables.SIGN).on(Tables.SIGN.CLASS_ID.eq(CLASS_INFO.ID))
              .leftJoin(SIGN_DETAIL).on(SIGN_DETAIL.SIGN_ID.eq(Tables.SIGN.ID))
              .where(Tables.TRAINEE.MEMBER_ID.eq(memberId)
                      .and(SIGN_DETAIL.MEMBER_ID.eq(memberId))
                      .and(Tables.CLASS_INFO.ID.in(classInfoIdSet))
                      .and(CLASS_INFO.STATUS.eq(STATUS_TRUE))
                      .and(SIGN_DETAIL.STATE.eq(AUDITED))
                      .and(CLASS_INFO.DELETE_FLAG.eq(DELETE_FLASE))
                      .and(SIGN_DETAIL.CREATE_TIME.between(startTime, endTime))
                      .and(Tables.TRAINEE.TYPE.eq(TRAINEE_TYPE_FORMAL))).groupBy(Tables.TRAINEE.MEMBER_ID)
              .fetch().intoMap(Tables.TRAINEE.MEMBER_ID, SIGN_DETAIL.ID.count());

      signedCountMap.putAll(signedCountMapReslt);

      //学员所有培训班的考勤次数
      Map<String, Integer> signCountMap1 = traineeCommonDao.execute(e -> e.select(Tables.TRAINEE.MEMBER_ID,Tables.SIGN.ID.count()))
              .from(Tables.TRAINEE).leftJoin(CLASS_INFO).on(Tables.TRAINEE.CLASS_ID.eq(CLASS_INFO.ID))
              .leftJoin(Tables.SIGN).on(Tables.SIGN.CLASS_ID.eq(CLASS_INFO.ID))
              .leftJoin(SIGN_DETAIL).on(SIGN_DETAIL.SIGN_ID.eq(Tables.SIGN.ID))
              .where(Tables.TRAINEE.MEMBER_ID.eq(memberId).and(SIGN_DETAIL.MEMBER_ID.eq(memberId))
                      .and(Tables.CLASS_INFO.ID.in(classInfoIdSet))
                      .and(CLASS_INFO.DELETE_FLAG.eq(DELETE_FLASE)).and(Tables.TRAINEE.TYPE.eq(TRAINEE_TYPE_FORMAL))
                      .and(SIGN_DETAIL.CREATE_TIME.between(startTime, endTime))
                      .and(CLASS_INFO.STATUS.eq(STATUS_TRUE))).groupBy(Tables.TRAINEE.MEMBER_ID)
              .fetch().intoMap(Tables.TRAINEE.MEMBER_ID, SIGN_DETAIL.ID.count());


      signCountMap.putAll(signCountMap1);
    }


    for (String memberId : signedCountMap.keySet()) {
      Integer signed = signedCountMap.get(memberId);
      Integer total = signCountMap.get(memberId);

      if (total == 0 || signed == 0) {

        resultMap.put(memberId, "0");
      } else {
        BigDecimal average = BigDecimal.valueOf(signed)
                .multiply(BigDecimal.valueOf(100))
                .divide(BigDecimal.valueOf(total), 2, BigDecimal.ROUND_HALF_UP);

        resultMap.put(memberId, average.toString());
      }
    }

    return resultMap;
  }


  /**
   * 平均满意度获取
   * @param memberIdSet
   * @param classInfoIdSet
   * @return
   */
  private Map<String, BigDecimal> getSatisfactionSum(Set<String> memberIdSet, Set<String> classInfoIdSet) {

    Map<String, BigDecimal> resultMap = new HashMap<>();

    Map<String, BigDecimal> satisfactionSumMap = classDao.execute(r ->
            r.select(RESEARCH_RECORD.MEMBER_ID, RESEARCH_ANSWER_RECORD.ANSWER.sum())
                    .from(RESEARCH_QUESTIONARY)
                    .leftJoin(RESEARCH_RECORD).on(RESEARCH_QUESTIONARY.ID.eq(RESEARCH_RECORD.RESEARCH_QUESTIONARY_ID))
                    .leftJoin(RESEARCH_ANSWER_RECORD).on(RESEARCH_RECORD.ID.eq(RESEARCH_ANSWER_RECORD.RESEARCH_RECORD_ID))
                    .leftJoin(QUESTIONNAIRE_QUESTION_TYPE).on(RESEARCH_ANSWER_RECORD.QUESTIONNAIRE_QUESTION_ID.eq(QUESTIONNAIRE_QUESTION_TYPE.ID))
                    .leftJoin(CLASS_INFO).on(RESEARCH_QUESTIONARY.CLASS_ID.eq(CLASS_INFO.ID))
                    .where(RESEARCH_RECORD.MEMBER_ID.in(memberIdSet)
                            .and(QUESTIONNAIRE_QUESTION_TYPE.LEVEL.eq(Short.parseShort(QuestionnaireQuestion.QUESTION_LEVEL_COURSE.toString())))
                            .and(CLASS_INFO.STATUS.eq(STATUS_TRUE))
                            .and(CLASS_INFO.ID.in(classInfoIdSet))
                            .and(CLASS_INFO.DELETE_FLAG.eq(DELETE_FLASE)))
                    .groupBy(RESEARCH_RECORD.MEMBER_ID)
                    .fetch().intoMap(Tables.TRAINEE.MEMBER_ID, RESEARCH_ANSWER_RECORD.ANSWER.sum()));


    Map<String, Integer> satisfactionCountMap = classDao.execute(r ->
            r.select(RESEARCH_RECORD.MEMBER_ID, RESEARCH_QUESTIONARY.CLASS_ID.countDistinct())
                    .from(RESEARCH_QUESTIONARY)
                    .leftJoin(RESEARCH_RECORD).on(RESEARCH_QUESTIONARY.ID.eq(RESEARCH_RECORD.RESEARCH_QUESTIONARY_ID))
                    .leftJoin(RESEARCH_ANSWER_RECORD).on(RESEARCH_RECORD.ID.eq(RESEARCH_ANSWER_RECORD.RESEARCH_RECORD_ID))
                    .leftJoin(QUESTIONNAIRE_QUESTION_TYPE).on(RESEARCH_ANSWER_RECORD.QUESTIONNAIRE_QUESTION_ID.eq(QUESTIONNAIRE_QUESTION_TYPE.ID))
                    .leftJoin(CLASS_INFO).on(RESEARCH_QUESTIONARY.CLASS_ID.eq(CLASS_INFO.ID))
                    .where(RESEARCH_RECORD.MEMBER_ID.in(memberIdSet)
                            .and(QUESTIONNAIRE_QUESTION_TYPE.LEVEL.eq(Short.parseShort(QuestionnaireQuestion.QUESTION_LEVEL_COURSE.toString())))
                            .and(CLASS_INFO.STATUS.eq(STATUS_TRUE))
                            .and(CLASS_INFO.DELETE_FLAG.eq(DELETE_FLASE)))
                    .groupBy(RESEARCH_RECORD.MEMBER_ID)
                    .fetch().intoMap(Tables.TRAINEE.MEMBER_ID, RESEARCH_QUESTIONARY.CLASS_ID.countDistinct()));

    for (String memberId : satisfactionCountMap.keySet()) {

      Integer count = satisfactionCountMap.get(memberId);

      BigDecimal sum = satisfactionSumMap.get(memberId);

      if (sum != null) {
        sum = sum.setScale(0, RoundingMode.HALF_UP);
      }

      if (count == 0 || sum == null) {
        resultMap.put(memberId,BigDecimal.ZERO);
      }else {
        resultMap.put(memberId, sum.divide(BigDecimal.valueOf(count), 2, BigDecimal.ROUND_HALF_UP));
      }
    }


    return resultMap;
  }

}

