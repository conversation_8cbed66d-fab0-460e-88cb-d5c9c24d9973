package com.zxy.product.train.async.task;


import com.zxy.common.dao.support.CommonDao;
import com.zxy.common.message.provider.MessageSender;
import com.zxy.product.train.async.util.DateUtil;
import com.zxy.product.train.content.MessageHeaderContent;
import com.zxy.product.train.content.MessageTypeContent;
import com.zxy.product.train.entity.ClassInfo;
import com.zxy.product.train.entity.ClassstaffClass;
import com.zxy.product.train.entity.TrainChatGroupInfo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.zxy.product.train.jooq.Tables.CLASSSTAFF_CLASS;
import static com.zxy.product.train.jooq.Tables.MEMBER;
import static com.zxy.product.train.jooq.Tables.PROJECT;
import static com.zxy.product.train.jooq.Tables.TRAIN_CHAT_GROUP_INFO;
import static com.zxy.product.train.jooq.tables.ClassInfo.CLASS_INFO;

/**
 * @Auther: xxh
 * @Date: 2025/8/27 - 08 - 27 - 16:23
 * @Description: 若培训班在报名日前仍未有班务人员手动建群，则在报道日0点自动建群，群主为成员管理-班主任列表内第一位班主任。
 * 若班主任列表为空，则不触发自动建群
 * @version: 1.0
 */
@Component
public class TrainGroupTask {

    private static final Logger LOGGER = LoggerFactory.getLogger(TrainGroupTask.class);

    private CommonDao<ClassstaffClass> dao;

    private CommonDao<ClassInfo> classDao;

    private MessageSender messageSender;

    private CommonDao<TrainChatGroupInfo> trainChatGroupInfoDao;

    @Autowired
    public void setTrainChatGroupInfoDao(CommonDao<TrainChatGroupInfo> trainChatGroupInfoDao) {
        this.trainChatGroupInfoDao = trainChatGroupInfoDao;
    }
    @Autowired
    public void setMessageSender(MessageSender messageSender) {
        this.messageSender = messageSender;
    }

    @Autowired
    public void setClassDao(CommonDao<ClassInfo> classDao) {
        this.classDao = classDao;
    }

    @Autowired
    public void setDao(CommonDao<ClassstaffClass> dao) {
        this.dao = dao;
    }

    /**
     * 查询第一个班主任
     * @param classIds
     * @return
     */
    private List<ClassstaffClass> getClassStaff(List<String> classIds){
       return dao.execute(e->e.select(CLASSSTAFF_CLASS.CLASS_ID,CLASSSTAFF_CLASS.MEMBER_ID,MEMBER.FULL_NAME)
                .from(CLASSSTAFF_CLASS)
                .leftJoin(MEMBER).on(CLASSSTAFF_CLASS.MEMBER_ID.eq(MEMBER.ID))
                .where(CLASSSTAFF_CLASS.CLASS_ID.in(classIds), CLASSSTAFF_CLASS.DELETE_FLAG.eq(ClassstaffClass.DELETE_FLASE),CLASSSTAFF_CLASS.SORT.eq(1))
                .fetch(r->{
                    ClassstaffClass into = r.into(ClassstaffClass.class);
                    into.setClassTeacher(r.getValue(MEMBER.FULL_NAME));
                    return into;
                }));
    }

    /**
     * 查询到时间的班级
     * @return
     */
    private List<ClassInfo> getClassInfo(Integer page ,Integer pageSize){
        return classDao.execute(e->e.select(CLASS_INFO.ID,PROJECT.NAME)
                .from(CLASS_INFO)
                .leftJoin(PROJECT).on(CLASS_INFO.PROJECT_ID.eq(PROJECT.ID))
                .where(CLASS_INFO.DELETE_FLAG.eq(ClassInfo.DELETE_FLASE),
                CLASS_INFO.ARRIVE_DATE.between(DateUtil.getYesterdayEndTime(System.currentTimeMillis()), DateUtil.getTimesnightByLong(System.currentTimeMillis())))
                .limit((page-1)*pageSize, pageSize)
                .fetch(r->{
                    ClassInfo classInfo = r.into(ClassInfo.class);
                    classInfo.setClassName(r.getValue(PROJECT.NAME));
                    return classInfo;
                })
        );
    }

    /**
     * 查询已经建群的班级
     * @param classIds
     * @return
     */
    private List<String> getClassIdByChatGroup(List<String> classIds){
       return trainChatGroupInfoDao.execute(e->e.select(TRAIN_CHAT_GROUP_INFO.CLASS_ID).from(TRAIN_CHAT_GROUP_INFO)
                .where(TRAIN_CHAT_GROUP_INFO.CLASS_ID.in(classIds))
                .fetch(TRAIN_CHAT_GROUP_INFO.CLASS_ID));
    }

    @Scheduled(cron = "0 0/20 * * * ? ")
    public void activeAutoGroupTask() {
        LOGGER.warn("开始建群");
        //得到所有报道日是今天的数据
        int page = 1;
        Integer pageSize = 100;
        List<ClassInfo> classInfo = new ArrayList<>();
        do {
            classInfo = getClassInfo(page, pageSize);
            if(!CollectionUtils.isEmpty(classInfo)){
                List<String> classIds = classInfo.stream().map(ClassInfo::getId).collect(Collectors.toList());
                //查询以及建群的数据
                List<String> classIdByChatGroup = getClassIdByChatGroup(classIds);
                //去掉已经创建了的数据
                List<ClassInfo> filterData= classInfo.stream().filter(r -> !classIdByChatGroup.contains(r.getId())).collect(Collectors.toList());
                if(CollectionUtils.isEmpty(filterData)){
                    return;
                }
                //查询是否有班主任
                List<ClassstaffClass> classStaff = getClassStaff(filterData.stream().map(ClassInfo::getId).collect(Collectors.toList()));
                //班主任不为空，创建群聊
                if(!CollectionUtils.isEmpty(classStaff)){
                    Map<String, ClassstaffClass> classMap = classStaff.stream().collect(Collectors.toMap(ClassstaffClass::getClassId, Function.identity(), (p, q) -> q));
                    filterData.forEach(item->{
                        ClassstaffClass classstaffClass = classMap.get(item.getId());
                        //有班主任，创建创建群
                        if(Objects.nonNull(classstaffClass)){
                            messageSender.send(MessageTypeContent.CHAT_GROUP_CREAT,
                                    MessageHeaderContent.CLASSID, item.getId(),
                                    MessageHeaderContent.MEMBERID, classstaffClass.getMemberId(),
                                    MessageHeaderContent.MEMBER_NAME, classstaffClass.getClassTeacher(),
                                    MessageHeaderContent.NAME, item.getClassName()
                            );
                        }
                    });
                }
            }
            page++;
        }while (!CollectionUtils.isEmpty(classInfo) && Objects.equals(classInfo.size(), pageSize));

    }





}
